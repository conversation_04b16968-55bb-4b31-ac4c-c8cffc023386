name: Deploy ns-doorservice-go to EC2

on:
  push:
    branches: ["main"]

permissions:
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    #environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: "1.21"

      - name: Build application
        run: |
          go mod tidy
          go build -o doorservice cmd/main.go

      - name: deploy to EC2
        env:
          SSH_PRIVATE_KEY: ${{ secrets.EC2_SSH_KEY }}
          EC2_HOST: ${{ secrets.EC2_HOST }}
          PORT: ${{secrets.PORT}}
          EC2_USER: ubuntu
          DB_STR: ${{ secrets.DB_STR }}
          #AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          #AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          #AWS_REGION: ${{ secrets.AWS_REGION }}
          GOOSE_DRIVER: ${{ secrets.GOOSE_DRIVER }}
          GOOSE_DBSTRING: ${{ secrets.GOOSE_DBSTRING }}
          GOOSE_MIGRATION_DIR: ${{ secrets.GOOSE_MIGRATION_DIR }}
          #JWT_SECRET: ${{ secrets.JWT_SECRET }}
          #MAINBRANCHID: ${{ secrets.MAINBRANCHID }}
          #JWT_EXP_HOUR: ${{ secrets.JWT_EXP_HOUR }}

        run: |
          echo "$SSH_PRIVATE_KEY" > private_key.pem
          chmod 600 private_key.pem

          # Copy binary to EC2
          scp -i private_key.pem -o StrictHostKeyChecking=no doorservice $EC2_USER@$EC2_HOST:/home/<USER>/doorservice

          # SSH into EC2 and configure the service
          ssh -i private_key.pem -o StrictHostKeyChecking=no $EC2_USER@$EC2_HOST << EOF

            sudo mkdir -p /usr/local/bin/nsdoorservice
            sudo mv /home/<USER>/doorservice /usr/local/bin/nsdoorservice/doorservice
            sudo chmod +x /usr/local/bin/nsdoorservice/doorservice

            #Create or update the .env file in /usr/local/bin
            # sudo bash -c "cat > /usr/local/bin/nsdoorservice/.env <<"ENV_EOF"
            # # Create or update the .env file in /usr/local/bin
            # sudo bash -c 'cat > /usr/local/bin/nsdoorservice/.env << EOF
            # PORT='$PORT'
            # DB_STR='$DB_STR'
            # GOOSE_DRIVER='$GOOSE_DRIVER'
            # GOOSE_DBSTRING='$GOOSE_DBSTRING'
            # GOOSE_MIGRATION_DIR='$GOOSE_MIGRATION_DIR'
            # EOF'

            # # Set permissions on .env file (secure it)
            # sudo chmod 600 /usr/local/bin/nsdoorservice/.env
            # sudo chown ubuntu:ubuntu /usr/local/bin/nsdoorservice/.env

            # Create or update the systemd service file
            if [ ! -f /etc/systemd/system/doorservice.service ]; then
              echo "Creating systemd service for doorservice go bin..."
            else
              echo "Updating systemd service for doorservice go bin..."
            fi

            sudo bash -c 'cat > /etc/systemd/system/doorservice.service <<"SERVICE_EOF"
            [Unit]
            Description=ns-doorservice-go application
            After=network.target

            [Service]
            ExecStart=/usr/local/bin/nsdoorservice/doorservice
            Restart=always
            User=ubuntu
            WorkingDirectory=/usr/local/bin/nsdoorservice
            EnvironmentFile=/usr/local/bin/nsdoorservice/.env

            [Install]
            WantedBy=multi-user.target
            SERVICE_EOF'

            sudo systemctl daemon-reload
            sudo systemctl enable nsserver
            sudo systemctl restart nsserver
          EOF

          rm -f private_key.pem  # Cleanup private key
