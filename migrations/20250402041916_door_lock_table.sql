-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS reg_door_lock (
  id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  device_name VARCHAR(150) NOT NULL,
  location VARCHAR(150) NOT NULL,
  branch_id BIGINT NOT NULL,
  api_key VARCHAR(150) NOT NULL,
  meta TEXT,
  session_key VARCHAR(150),
  last_session_key TIMESTAMP,
  is_online BOOLEAN NOT NULL DEFAULT FALSE,
  is_ban BOOLEAN NOT NULL DEFAULT FALSE,
  last_online TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (branch_id) REFERENCES HR_Branch(id)
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE reg_door_lock;
-- +goose StatementEnd
