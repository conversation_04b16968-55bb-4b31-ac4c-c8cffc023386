-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS attendee (
  id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  attendee_id BIGINT NOT NULL,
  time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  state VARCHAR(10) NOT NULL,
  FOREIGN KEY (attendee_id) REFERENCES door_lock_users(attendee_id)
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE attendee;
-- +goose StatementEnd
