-- +goose Up
-- +goose StatementBegin
ALTER TABLE `attendee`
  ADD COLUMN IF NOT EXISTS `subscription` VARCHAR(50) NULL,
  ADD COLUMN IF NOT EXISTS `booktype` VARCHAR(20) NULL,
  ADD COLUMN IF NOT EXISTS `service_data` TEXT NULL,
  ADD COLUMN IF NOT EXISTS `gym_data` TEXT NULL;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
ALTER TABLE `attendee`
  DROP COLUMN IF EXISTS `subscription`,
  DROP COLUMN IF EXISTS `booktype`,
  DROP COLUMN IF EXISTS `service_data`,
  DROP COLUMN IF EXISTS `gym_data`;
-- +goose StatementEnd
