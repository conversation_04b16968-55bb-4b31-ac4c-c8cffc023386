-- +goose Up
-- +goose StatementBegin
CREATE TABLE IF NOT EXISTS door_lock_users (
    attendee_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    branch_id BIGINT NOT NULL,
    subscription_id BIGINT NOT NULL,
    last_in TIMESTAMP,
    last_out <PERSON><PERSON><PERSON><PERSON><PERSON>,
    current_state VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (branch_id) REFERENCES HR_Branch(id)
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE door_lock_users;
-- +goose StatementEnd
