package wspkg_test

import (
	"errors"
	"net/http"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	testutils "github.com/Terracode-Dev/ION_SEC_DOOR_WSS/testing/utils"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDoorClientWS_Success(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Setup valid token and device
	token := "valid-token"
	deviceID := "1"
	branchID := 1
	
	setup.TokenStore.AddDoorLockToken(token, deviceID, branchID)
	setup.DB.SetValidDevice(1, int64(branchID), "Test Door Lock")
	
	// Create test request
	req, rec := testutils.CreateTestWebSocketRequest(token)
	c := testutils.CreateEchoContext(req, rec, setup.Echo)
	
	// Execute the handler
	err := setup.WSCtx.DoorClientWS(c)
	
	// Assertions
	assert.NoError(t, err)
	
	// Check that client was added to manager
	assert.Equal(t, 1, setup.Manager.GetClientCount())
	
	// Check that door client was created in state
	clients := setup.WSCtx.ClientState.GetDoorClientsByBranch(branchID)
	assert.Len(t, clients, 1)
	assert.Equal(t, "Test Door Lock", clients[0].Name)
	
	// Check that token was removed
	_, valid := setup.TokenStore.ValidateToken(token)
	assert.False(t, valid)
	
	// Verify no errors were logged
	testutils.AssertNoErrors(t, setup.Logger)
}

func TestDoorClientWS_InvalidToken(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Create test request with invalid token
	req, rec := testutils.CreateTestWebSocketRequest("invalid-token")
	c := testutils.CreateEchoContext(req, rec, setup.Echo)
	
	// Execute the handler
	err := setup.WSCtx.DoorClientWS(c)
	
	// Assertions
	assert.NoError(t, err) // Handler should not return error, but HTTP error response
	assert.Equal(t, http.StatusUnauthorized, rec.Code)
	
	// Check that no client was added
	assert.Equal(t, 0, setup.Manager.GetClientCount())
	
	// Check that error was logged
	testutils.AssertErrorLogged(t, setup.Logger, "Token Validation Error @ DoorClientWS")
}

func TestDoorClientWS_ExpiredToken(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Setup expired token
	token := "expired-token"
	deviceID := "1"
	branchID := 1
	
	setup.TokenStore.AddExpiredToken(token, deviceID, "door-lock", branchID)
	
	// Create test request
	req, rec := testutils.CreateTestWebSocketRequest(token)
	c := testutils.CreateEchoContext(req, rec, setup.Echo)
	
	// Execute the handler
	err := setup.WSCtx.DoorClientWS(c)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, http.StatusUnauthorized, rec.Code)
	assert.Equal(t, 0, setup.Manager.GetClientCount())
}

func TestDoorClientWS_WrongConnectionType(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Setup token with wrong connection type
	token := "qr-token"
	setup.TokenStore.AddValidToken(token, "1", "qr-in", 1)
	
	// Create test request
	req, rec := testutils.CreateTestWebSocketRequest(token)
	c := testutils.CreateEchoContext(req, rec, setup.Echo)
	
	// Execute the handler
	err := setup.WSCtx.DoorClientWS(c)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, rec.Code)
	assert.Equal(t, 0, setup.Manager.GetClientCount())
	
	testutils.AssertErrorLogged(t, setup.Logger, "Connection Type Error @ DoorClientWS")
}

func TestDoorClientWS_InvalidClientID(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Setup token with invalid client ID
	token := "invalid-id-token"
	setup.TokenStore.AddDoorLockToken(token, "not-a-number", 1)
	
	// Create test request
	req, rec := testutils.CreateTestWebSocketRequest(token)
	c := testutils.CreateEchoContext(req, rec, setup.Echo)
	
	// Execute the handler
	err := setup.WSCtx.DoorClientWS(c)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, rec.Code)
	assert.Equal(t, 0, setup.Manager.GetClientCount())
	
	testutils.AssertErrorLogged(t, setup.Logger, "invalid Client ID @ DoorClientWS")
}

func TestDoorClientWS_DatabaseError(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Setup valid token but database error
	token := "valid-token"
	setup.TokenStore.AddDoorLockToken(token, "1", 1)
	setup.DB.SetDatabaseError(errors.New("database connection failed"))
	
	// Create test request
	req, rec := testutils.CreateTestWebSocketRequest(token)
	c := testutils.CreateEchoContext(req, rec, setup.Echo)
	
	// Execute the handler
	err := setup.WSCtx.DoorClientWS(c)
	
	// Assertions
	assert.NoError(t, err)
	assert.Equal(t, http.StatusInternalServerError, rec.Code)
	assert.Equal(t, 0, setup.Manager.GetClientCount())
	
	testutils.AssertErrorLogged(t, setup.Logger, "Error fetching device data @ DoorClientWS")
}

func TestDoorClientWS_ManagerAtCapacity(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Fill manager to capacity (assuming default is 1000, we'll set a lower limit for testing)
	// This would require modifying the manager or creating a custom one with lower limits
	// For now, we'll test the AddExistingClient failure scenario differently
	
	// Setup valid token and device
	token := "valid-token"
	setup.TokenStore.AddDoorLockToken(token, "1", 1)
	setup.DB.SetValidDevice(1, 1, "Test Door Lock")
	
	// Create test request
	req, rec := testutils.CreateTestWebSocketRequest(token)
	c := testutils.CreateEchoContext(req, rec, setup.Echo)
	
	// Execute the handler
	err := setup.WSCtx.DoorClientWS(c)
	
	// For this test, we expect success since we can't easily simulate capacity issues
	// In a real scenario, you'd create a manager with maxClients=0 or similar
	assert.NoError(t, err)
}

func TestDoorClientWS_ConcurrentConnections(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Setup multiple valid tokens
	numConnections := 5
	tokens := make([]string, numConnections)
	
	for i := 0; i < numConnections; i++ {
		tokens[i] = "token-" + string(rune('0'+i))
		setup.TokenStore.AddDoorLockToken(tokens[i], "1", 1)
	}
	
	setup.DB.SetValidDevice(1, 1, "Test Door Lock")
	
	// Create multiple concurrent connections
	done := make(chan bool, numConnections)
	
	for i := 0; i < numConnections; i++ {
		go func(token string) {
			defer func() { done <- true }()
			
			req, rec := testutils.CreateTestWebSocketRequest(token)
			c := testutils.CreateEchoContext(req, rec, setup.Echo)
			
			err := setup.WSCtx.DoorClientWS(c)
			assert.NoError(t, err)
		}(tokens[i])
	}
	
	// Wait for all connections to complete
	for i := 0; i < numConnections; i++ {
		select {
		case <-done:
			// Connection completed
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for concurrent connections")
		}
	}
	
	// Verify all clients were added
	testutils.WaitForCondition(t, func() bool {
		return setup.Manager.GetClientCount() == numConnections
	}, 2*time.Second, "all clients to be added to manager")
	
	// Verify all door clients were created
	clients := setup.WSCtx.ClientState.GetDoorClientsByBranch(1)
	assert.Len(t, clients, numConnections)
}
