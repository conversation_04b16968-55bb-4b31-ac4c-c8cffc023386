package wspkg_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	testutils "github.com/Terracode-Dev/ION_SEC_DOOR_WSS/testing/utils"
	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestIntegration_CompleteWebSocketFlow tests the complete WebSocket connection flow
func TestIntegration_CompleteWebSocketFlow(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Setup valid token and device
	token := "integration-test-token"
	deviceID := "1"
	branchID := 1
	
	setup.TokenStore.AddDoorLockToken(token, deviceID, branchID)
	setup.DB.SetValidDevice(1, int64(branchID), "Integration Test Door")
	
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		c := setup.Echo.NewContext(r, w)
		err := setup.WSCtx.DoorClientWS(c)
		if err != nil {
			t.Errorf("DoorClientWS failed: %v", err)
		}
	}))
	defer server.Close()
	
	// Convert HTTP URL to WebSocket URL
	wsURL := "ws" + server.URL[4:] + "?token=" + token
	
	// Connect to WebSocket
	dialer := websocket.Dialer{
		HandshakeTimeout: 5 * time.Second,
	}
	
	conn, resp, err := dialer.Dial(wsURL, nil)
	require.NoError(t, err)
	defer conn.Close()
	
	assert.Equal(t, http.StatusSwitchingProtocols, resp.StatusCode)
	
	// Wait for connection to be established
	testutils.WaitForCondition(t, func() bool {
		return setup.Manager.GetClientCount() > 0
	}, 2*time.Second, "client to be added to manager")
	
	// Verify client was added
	assert.Equal(t, 1, setup.Manager.GetClientCount())
	
	// Verify door client was created
	clients := setup.WSCtx.ClientState.GetDoorClientsByBranch(branchID)
	assert.Len(t, clients, 1)
	assert.Equal(t, "Integration Test Door", clients[0].Name)
	
	// Test sending a message (this would require event handlers to be implemented)
	message := wspkg.NewMessage("ping", []byte(`{"timestamp": "` + time.Now().Format(time.RFC3339) + `"}`))
	err = conn.WriteJSON(message)
	assert.NoError(t, err)
	
	// Close connection gracefully
	err = conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
	assert.NoError(t, err)
	
	// Wait for cleanup
	testutils.WaitForCondition(t, func() bool {
		return setup.Manager.GetClientCount() == 0
	}, 2*time.Second, "client to be removed from manager")
}

// TestIntegration_MultipleClients tests multiple concurrent WebSocket connections
func TestIntegration_MultipleClients(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	numClients := 3
	tokens := make([]string, numClients)
	
	// Setup multiple valid tokens and devices
	for i := 0; i < numClients; i++ {
		tokens[i] = "multi-client-token-" + string(rune('0'+i))
		setup.TokenStore.AddDoorLockToken(tokens[i], "1", 1)
	}
	setup.DB.SetValidDevice(1, 1, "Multi Client Test Door")
	
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		c := setup.Echo.NewContext(r, w)
		err := setup.WSCtx.DoorClientWS(c)
		if err != nil {
			t.Errorf("DoorClientWS failed: %v", err)
		}
	}))
	defer server.Close()
	
	connections := make([]*websocket.Conn, numClients)
	
	// Connect multiple clients
	for i := 0; i < numClients; i++ {
		wsURL := "ws" + server.URL[4:] + "?token=" + tokens[i]
		
		dialer := websocket.Dialer{
			HandshakeTimeout: 5 * time.Second,
		}
		
		conn, resp, err := dialer.Dial(wsURL, nil)
		require.NoError(t, err)
		defer conn.Close()
		
		assert.Equal(t, http.StatusSwitchingProtocols, resp.StatusCode)
		connections[i] = conn
	}
	
	// Wait for all connections to be established
	testutils.WaitForCondition(t, func() bool {
		return setup.Manager.GetClientCount() == numClients
	}, 5*time.Second, "all clients to be added to manager")
	
	// Verify all clients were added
	assert.Equal(t, numClients, setup.Manager.GetClientCount())
	
	// Verify all door clients were created
	clients := setup.WSCtx.ClientState.GetDoorClientsByBranch(1)
	assert.Len(t, clients, numClients)
	
	// Close all connections
	for i := 0; i < numClients; i++ {
		err := connections[i].WriteMessage(websocket.CloseMessage, 
			websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		assert.NoError(t, err)
		connections[i].Close()
	}
	
	// Wait for cleanup
	testutils.WaitForCondition(t, func() bool {
		return setup.Manager.GetClientCount() == 0
	}, 5*time.Second, "all clients to be removed from manager")
}

// TestIntegration_ErrorScenarios tests various error scenarios
func TestIntegration_ErrorScenarios(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	testCases := []struct {
		name           string
		token          string
		setupFunc      func()
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "Invalid Token",
			token:          "invalid-token",
			setupFunc:      func() {},
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "Token Validation Error @ DoorClientWS",
		},
		{
			name:  "Database Error",
			token: "db-error-token",
			setupFunc: func() {
				setup.TokenStore.AddDoorLockToken("db-error-token", "1", 1)
				setup.DB.SetDatabaseError(assert.AnError)
			},
			expectedStatus: http.StatusInternalServerError,
			expectedError:  "Error fetching device data @ DoorClientWS",
		},
		{
			name:  "Wrong Connection Type",
			token: "wrong-type-token",
			setupFunc: func() {
				setup.TokenStore.AddValidToken("wrong-type-token", "1", "qr-in", 1)
			},
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Connection Type Error @ DoorClientWS",
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Clear previous logs
			setup.Logger.Clear()
			
			// Setup test case
			tc.setupFunc()
			
			// Create test request
			req, rec := testutils.CreateTestWebSocketRequest(tc.token)
			c := testutils.CreateEchoContext(req, rec, setup.Echo)
			
			// Execute handler
			err := setup.WSCtx.DoorClientWS(c)
			
			// Verify response
			assert.NoError(t, err) // Handler should not return error, but HTTP error response
			assert.Equal(t, tc.expectedStatus, rec.Code)
			
			// Verify error was logged
			testutils.AssertErrorLogged(t, setup.Logger, tc.expectedError)
			
			// Verify no clients were added
			assert.Equal(t, 0, setup.Manager.GetClientCount())
		})
	}
}

// TestIntegration_EventRouting tests message routing through the event system
func TestIntegration_EventRouting(t *testing.T) {
	setup := testutils.NewTestSetup(t)
	
	// Setup event router with test handlers
	router := wspkg.NewEventRouter()
	
	receivedEvents := make([]string, 0)
	
	router.On("test-event", func(c wspkg.Ctx) error {
		receivedEvents = append(receivedEvents, "test-event")
		return nil
	})
	
	router.On("ping", func(c wspkg.Ctx) error {
		receivedEvents = append(receivedEvents, "ping")
		// Echo back a pong
		response := wspkg.NewMessage("pong", []byte(`{"message": "pong"}`))
		c.SendToClient(response)
		return nil
	})
	
	setup.Manager.SetRouter(router)
	
	// Setup valid token and device
	token := "event-test-token"
	setup.TokenStore.AddDoorLockToken(token, "1", 1)
	setup.DB.SetValidDevice(1, 1, "Event Test Door")
	
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		c := setup.Echo.NewContext(r, w)
		err := setup.WSCtx.DoorClientWS(c)
		if err != nil {
			t.Errorf("DoorClientWS failed: %v", err)
		}
	}))
	defer server.Close()
	
	// Connect to WebSocket
	wsURL := "ws" + server.URL[4:] + "?token=" + token
	dialer := websocket.Dialer{HandshakeTimeout: 5 * time.Second}
	
	conn, _, err := dialer.Dial(wsURL, nil)
	require.NoError(t, err)
	defer conn.Close()
	
	// Wait for connection
	testutils.WaitForCondition(t, func() bool {
		return setup.Manager.GetClientCount() > 0
	}, 2*time.Second, "client to connect")
	
	// Send test events
	testMessage := wspkg.NewMessage("test-event", []byte(`{"data": "test"}`))
	err = conn.WriteJSON(testMessage)
	assert.NoError(t, err)
	
	pingMessage := wspkg.NewMessage("ping", []byte(`{"timestamp": "now"}`))
	err = conn.WriteJSON(pingMessage)
	assert.NoError(t, err)
	
	// Give some time for message processing
	time.Sleep(100 * time.Millisecond)
	
	// Verify events were received
	assert.Contains(t, receivedEvents, "test-event")
	assert.Contains(t, receivedEvents, "ping")
}
