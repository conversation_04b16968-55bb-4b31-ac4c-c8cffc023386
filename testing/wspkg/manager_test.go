package wspkg_test

import (
	"net/http"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/testing/wspkg/mocks"
	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDefaultManager_Creation(t *testing.T) {
	manager, err := wspkg.DefaultManager()

	require.NoError(t, err)
	assert.NotNil(t, manager)
	assert.Equal(t, 0, manager.GetClientCount())
}

func TestManager_AddClient(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	// Since we can't easily create a real websocket.Conn for testing,
	// we'll test the basic client count functionality

	// Test that initially no clients exist
	assert.Equal(t, 0, manager.GetClientCount())
}

func TestManager_ClientLifecycle(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	logger := mocks.NewMockLogger()
	manager.SetLogger(logger)

	// Test GetClientCount
	assert.Equal(t, 0, manager.GetClientCount())

	// Test RemoveClient with non-existent client
	err = manager.RemoveClient("non-existent")
	assert.NoError(t, err)

	// Verify debug log was created for non-existent client removal
	logs := logger.GetLogs()
	found := false
	for _, log := range logs {
		if log.Message == "Attempted to remove non-existent client" {
			found = true
			break
		}
	}
	assert.True(t, found, "Expected debug log for non-existent client removal")
}

func TestManager_BroadcastMessage(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	logger := mocks.NewMockLogger()
	manager.SetLogger(logger)

	// Create a test message
	message := wspkg.NewMessage("test-event", []byte(`{"data": "test"}`))

	// Broadcast to empty manager (should not panic)
	manager.BroadcastMessage(message)

	// Verify debug log was created
	logs := logger.GetLogs()
	found := false
	for _, log := range logs {
		if log.Message == "Message broadcasted" {
			found = true
			break
		}
	}
	assert.True(t, found, "Expected debug log for message broadcast")
}

func TestManager_BroadcastMessageToGroup(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	logger := mocks.NewMockLogger()
	manager.SetLogger(logger)

	// Create a test message
	message := wspkg.NewMessage("test-event", []byte(`{"data": "test"}`))

	// Broadcast to empty group
	group := []string{"client1", "client2", "client3"}
	manager.BroadcastMessageToGroup(message, group)

	// Verify debug log was created
	logs := logger.GetLogs()
	found := false
	for _, log := range logs {
		if log.Message == "Group message broadcasted" {
			found = true
			break
		}
	}
	assert.True(t, found, "Expected debug log for group message broadcast")
}

func TestManager_SetLogger(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	// Test setting a new logger
	newLogger := mocks.NewMockLogger()
	manager.SetLogger(newLogger)

	// Test setting nil logger (should not panic)
	manager.SetLogger(nil)
}

func TestManager_SetErrorHandler(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	logger := mocks.NewMockLogger()
	manager.SetLogger(logger)

	// Create a custom error handler
	errorHandler := wspkg.NewDefaultErrorHandler(logger)
	manager.SetErrorHandler(errorHandler)

	// Verify info log was created
	logs := logger.GetInfoLogs()
	found := false
	for _, log := range logs {
		if log.Message == "Error handler updated for WebSocket manager" {
			found = true
			break
		}
	}
	assert.True(t, found, "Expected info log for error handler update")
}

func TestManager_SetAllowedOrigins(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	logger := mocks.NewMockLogger()
	manager.SetLogger(logger)

	// Test setting allowed origins
	origins := []string{"http://localhost:3000", "https://example.com"}
	manager.SetAllowedOrigins(origins)

	// Verify info log was created
	logs := logger.GetInfoLogs()
	found := false
	for _, log := range logs {
		if log.Message == "Allowed origins updated" {
			found = true
			break
		}
	}
	assert.True(t, found, "Expected info log for allowed origins update")

	// Test wildcard origin
	manager.SetAllowedOrigins([]string{"*"})
}

func TestManager_Configuration(t *testing.T) {
	// Test default configuration
	config := wspkg.DefaultConfig()

	assert.Equal(t, 60*time.Second, config.ReadTimeout)
	assert.Equal(t, 10*time.Second, config.WriteTimeout)
	assert.Equal(t, 54*time.Second, config.PingInterval)
	assert.Equal(t, 10*time.Second, config.PongTimeout)
	assert.Equal(t, 1024, config.ReadBufferSize)
	assert.Equal(t, 1024, config.WriteBufferSize)
	assert.Equal(t, 100, config.SendChannelSize)
	assert.Equal(t, 1000, config.MaxClients)
	assert.Equal(t, 1024*1024, config.MaxMessageSize)
	assert.Equal(t, []string{"*"}, config.AllowedOrigins)
	assert.False(t, config.EnableCompression)
	assert.True(t, config.EnablePing)
	assert.True(t, config.LogConnection)
	assert.True(t, config.LogError)
}

func TestCreateManager_WithCustomConfig(t *testing.T) {
	upgrader := websocket.Upgrader{
		ReadBufferSize:  2048,
		WriteBufferSize: 2048,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	config := &wspkg.Config{
		ReadTimeout:    30 * time.Second,
		WriteTimeout:   5 * time.Second,
		MaxClients:     500,
		LogConnection:  false,
		LogError:       false,
		AllowedOrigins: []string{"https://example.com"},
	}

	manager, err := wspkg.CreateManager(upgrader, config)

	require.NoError(t, err)
	assert.NotNil(t, manager)
	assert.Equal(t, 0, manager.GetClientCount())

	// Use the upgrader to avoid unused variable warning
	_ = upgrader
}

func TestManager_GetClient(t *testing.T) {
	manager, err := wspkg.DefaultManager()
	require.NoError(t, err)

	// Test getting non-existent client
	client, exists := manager.GetClient("non-existent")
	assert.Nil(t, client)
	assert.False(t, exists)
}
