package wspkg_test

import (
	"errors"
	"testing"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/stretchr/testify/assert"
)

func TestEventRouter_Creation(t *testing.T) {
	router := wspkg.NewEventRouter()

	assert.NotNil(t, router)
}

func TestEventRouter_RegisterHandler(t *testing.T) {
	router := wspkg.NewEventRouter()

	// Test registering a handler
	handlerCalled := false
	testHandler := func(c wspkg.Ctx) error {
		handlerCalled = true
		return nil
	}

	router.On("test-event", testHandler)

	// Create a test context
	message := wspkg.NewMessage("test-event", []byte(`{"data": "test"}`))
	ctx := wspkg.Ctx{
		Data: message,
	}

	// Handle the event
	err := router.Handle(ctx)

	assert.NoError(t, err)
	assert.True(t, handlerCalled)
}

func TestEventRouter_UnknownEvent(t *testing.T) {
	router := wspkg.NewEventRouter()

	// Create a test context with unknown event
	message := wspkg.NewMessage("unknown-event", []byte(`{"data": "test"}`))
	ctx := wspkg.Ctx{
		Data: message,
	}

	// Handle the event
	err := router.Handle(ctx)

	assert.Error(t, err)
	assert.Equal(t, "event not found", err.Error())
}

func TestEventRouter_HandlerError(t *testing.T) {
	router := wspkg.NewEventRouter()

	// Register a handler that returns an error
	expectedError := errors.New("handler error")
	errorHandler := func(c wspkg.Ctx) error {
		return expectedError
	}

	router.On("error-event", errorHandler)

	// Create a test context
	message := wspkg.NewMessage("error-event", []byte(`{"data": "test"}`))
	ctx := wspkg.Ctx{
		Data: message,
	}

	// Handle the event
	err := router.Handle(ctx)

	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
}

func TestEventRouter_MultipleHandlers(t *testing.T) {
	router := wspkg.NewEventRouter()

	// Register multiple handlers for different events
	handler1Called := false
	handler2Called := false

	router.On("event1", func(c wspkg.Ctx) error {
		handler1Called = true
		return nil
	})

	router.On("event2", func(c wspkg.Ctx) error {
		handler2Called = true
		return nil
	})

	// Test first event
	message1 := wspkg.NewMessage("event1", []byte(`{"data": "test1"}`))
	ctx1 := wspkg.Ctx{Data: message1}

	err := router.Handle(ctx1)
	assert.NoError(t, err)
	assert.True(t, handler1Called)
	assert.False(t, handler2Called)

	// Reset flags
	handler1Called = false
	handler2Called = false

	// Test second event
	message2 := wspkg.NewMessage("event2", []byte(`{"data": "test2"}`))
	ctx2 := wspkg.Ctx{Data: message2}

	err = router.Handle(ctx2)
	assert.NoError(t, err)
	assert.False(t, handler1Called)
	assert.True(t, handler2Called)
}

func TestEventRouter_OverwriteHandler(t *testing.T) {
	router := wspkg.NewEventRouter()

	// Register initial handler
	firstHandlerCalled := false
	secondHandlerCalled := false

	router.On("test-event", func(c wspkg.Ctx) error {
		firstHandlerCalled = true
		return nil
	})

	// Overwrite with second handler
	router.On("test-event", func(c wspkg.Ctx) error {
		secondHandlerCalled = true
		return nil
	})

	// Create a test context
	message := wspkg.NewMessage("test-event", []byte(`{"data": "test"}`))
	ctx := wspkg.Ctx{Data: message}

	// Handle the event
	err := router.Handle(ctx)

	assert.NoError(t, err)
	assert.False(t, firstHandlerCalled)
	assert.True(t, secondHandlerCalled)
}

func TestEventRouter_HandlerWithContext(t *testing.T) {
	router := wspkg.NewEventRouter()

	// Register a handler that uses context data
	var receivedData []byte
	var receivedEvent string

	router.On("data-event", func(c wspkg.Ctx) error {
		receivedEvent = c.Data.GetEvent()
		receivedData = c.Data.GetData()
		return nil
	})

	// Create a test context with specific data
	testData := []byte(`{"message": "hello world"}`)
	message := wspkg.NewMessage("data-event", testData)
	ctx := wspkg.Ctx{Data: message}

	// Handle the event
	err := router.Handle(ctx)

	assert.NoError(t, err)
	assert.Equal(t, "data-event", receivedEvent)
	assert.Equal(t, testData, receivedData)
}

func TestEventRouter_HandlerWithBinding(t *testing.T) {
	router := wspkg.NewEventRouter()

	// Define a struct for binding
	type TestPayload struct {
		Action string `json:"action"`
		Value  int    `json:"value"`
	}

	var receivedPayload TestPayload

	router.On("bind-event", func(c wspkg.Ctx) error {
		return c.Bind(&receivedPayload)
	})

	// Create a test context with JSON data
	testData := []byte(`{"action": "update", "value": 42}`)
	message := wspkg.NewMessage("bind-event", testData)
	ctx := wspkg.Ctx{Data: message}

	// Handle the event
	err := router.Handle(ctx)

	assert.NoError(t, err)
	assert.Equal(t, "update", receivedPayload.Action)
	assert.Equal(t, 42, receivedPayload.Value)
}

func TestEventRouter_HandlerWithInvalidBinding(t *testing.T) {
	router := wspkg.NewEventRouter()

	// Define a struct for binding
	type TestPayload struct {
		Action string `json:"action"`
		Value  int    `json:"value"`
	}

	var receivedPayload TestPayload

	router.On("invalid-bind-event", func(c wspkg.Ctx) error {
		return c.Bind(&receivedPayload)
	})

	// Create a test context with invalid JSON data
	testData := []byte(`{"action": "update", "value": "not-a-number"}`)
	message := wspkg.NewMessage("invalid-bind-event", testData)
	ctx := wspkg.Ctx{Data: message}

	// Handle the event
	err := router.Handle(ctx)

	assert.Error(t, err)
}

// Test Ctx functionality
func TestCtx_Creation(t *testing.T) {
	message := wspkg.NewMessage("test", []byte(`{}`))
	ctx := wspkg.Ctx{
		Data: message,
	}

	assert.Equal(t, message, ctx.Data)
	assert.Equal(t, []byte(`{}`), ctx.GetMsg())
}

func TestCtx_GetClientID(t *testing.T) {
	// Test with nil client
	ctx := wspkg.Ctx{}
	assert.Equal(t, "", ctx.GetClientID())

	// Test with mock client (would need actual client implementation)
	// This test would require modifying the Client struct or creating a mock
}

func TestCtx_IsClientOnline(t *testing.T) {
	// Test with nil client
	ctx := wspkg.Ctx{}
	assert.False(t, ctx.IsClientOnline())

	// Test with mock client (would need actual client implementation)
	// This test would require modifying the Client struct or creating a mock
}
