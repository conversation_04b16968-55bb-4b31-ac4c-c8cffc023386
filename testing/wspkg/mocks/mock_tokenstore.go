package mocks

import (
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
)

// MockTokenStore implements utils.TokenStore interface for testing
type MockTokenStore struct {
	tokens map[string]*utils.TokenInfo
}

func NewMockTokenStore() *MockTokenStore {
	return &MockTokenStore{
		tokens: make(map[string]*utils.TokenInfo),
	}
}

func (m *MockTokenStore) StoreToken(token, clientId, connType string, branchId int, reqId, apiKey string, expiration time.Time) {
	m.tokens[token] = &utils.TokenInfo{
		ClientId: clientId,
		ConnType: connType,
		BranchId: branchId,
		ReqId:    reqId,
		ApiKey:   apiKey,
		ExpDate:  expiration,
	}
}

func (m *MockTokenStore) ValidateToken(token string) (utils.TokenInfo, bool) {
	tokenData, exists := m.tokens[token]
	if !exists {
		return utils.TokenInfo{}, false
	}

	// Check if token is expired
	if time.Now().After(tokenData.ExpDate) {
		delete(m.tokens, token)
		return utils.TokenInfo{}, false
	}

	return *tokenData, true
}

func (m *MockTokenStore) RemoveToken(token string) {
	delete(m.tokens, token)
}

func (m *MockTokenStore) GetAllTokens() map[string]*utils.TokenInfo {
	return m.tokens
}

func (m *MockTokenStore) DeleteTokensByClientID(clientID string) {
	for token, data := range m.tokens {
		if data.ClientId == clientID {
			delete(m.tokens, token)
		}
	}
}

func (m *MockTokenStore) ResetTokenStore() {
	m.tokens = make(map[string]*utils.TokenInfo)
}

func (m *MockTokenStore) Shutdown() {
	// No-op for mock
}

func (m *MockTokenStore) DrawTUITokenStoreData() {
	// No-op for mock
}

func (m *MockTokenStore) SetIsOnlineFunc(fn func(string) bool) {
	// No-op for mock
}

// Helper methods for testing

// AddValidToken adds a valid token for testing
func (m *MockTokenStore) AddValidToken(token, clientId, connType string, branchId int) {
	m.StoreToken(token, clientId, connType, branchId, "", "test-api-key", time.Now().Add(5*time.Minute))
}

// AddExpiredToken adds an expired token for testing
func (m *MockTokenStore) AddExpiredToken(token, clientId, connType string, branchId int) {
	m.StoreToken(token, clientId, connType, branchId, "", "test-api-key", time.Now().Add(-1*time.Minute))
}

// AddDoorLockToken adds a valid door-lock token for testing
func (m *MockTokenStore) AddDoorLockToken(token, deviceId string, branchId int) {
	m.AddValidToken(token, deviceId, "door-lock", branchId)
}

// AddQRToken adds a valid QR token for testing
func (m *MockTokenStore) AddQRToken(token, clientId, qrType string, branchId int, reqDeviceId string) {
	m.StoreToken(token, clientId, qrType, branchId, reqDeviceId, "test-api-key", time.Now().Add(5*time.Minute))
}
