package mocks

import (
	"fmt"
	"sync"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
)

// MockLogger implements wspkg.Logger interface for testing
type MockLogger struct {
	mu       sync.RWMutex
	logs     []LogEntry
	logLevel LogLevel
}

type LogLevel int

const (
	DebugLevel LogLevel = iota
	InfoLevel
	WarnLevel
	ErrorLevel
	FatalLevel
)

type LogEntry struct {
	Level   LogLevel
	Message string
	Fields  []wspkg.Field
}

func NewMockLogger() *MockLogger {
	return &MockLogger{
		logs:     make([]LogEntry, 0),
		logLevel: DebugLevel, // Log everything by default
	}
}

func (m *MockLogger) Debug(msg string, fields ...wspkg.Field) {
	if m.logLevel <= DebugLevel {
		m.addLog(DebugLevel, msg, fields)
	}
}

func (m *MockLogger) Info(msg string, fields ...wspkg.Field) {
	if m.logLevel <= InfoLevel {
		m.addLog(InfoLevel, msg, fields)
	}
}

func (m *MockLogger) Warn(msg string, fields ...wspkg.Field) {
	if m.logLevel <= WarnLevel {
		m.addLog(WarnLevel, msg, fields)
	}
}

func (m *MockLogger) Error(msg string, fields ...wspkg.Field) {
	if m.logLevel <= ErrorLevel {
		m.addLog(ErrorLevel, msg, fields)
	}
}

func (m *MockLogger) Fatal(msg string, fields ...wspkg.Field) {
	m.addLog(FatalLevel, msg, fields)
	// In real implementation, this would call os.Exit(1)
	// For testing, we just log it
}

func (m *MockLogger) With(fields ...wspkg.Field) wspkg.Logger {
	// For simplicity, return the same logger
	// In a real implementation, this would create a new logger with additional fields
	return m
}

func (m *MockLogger) Sync() error {
	return nil
}

func (m *MockLogger) addLog(level LogLevel, msg string, fields []wspkg.Field) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.logs = append(m.logs, LogEntry{
		Level:   level,
		Message: msg,
		Fields:  fields,
	})
}

// Test helper methods

func (m *MockLogger) GetLogs() []LogEntry {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	// Return a copy to avoid race conditions
	logs := make([]LogEntry, len(m.logs))
	copy(logs, m.logs)
	return logs
}

func (m *MockLogger) GetLogsByLevel(level LogLevel) []LogEntry {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	var filtered []LogEntry
	for _, log := range m.logs {
		if log.Level == level {
			filtered = append(filtered, log)
		}
	}
	return filtered
}

func (m *MockLogger) GetErrorLogs() []LogEntry {
	return m.GetLogsByLevel(ErrorLevel)
}

func (m *MockLogger) GetInfoLogs() []LogEntry {
	return m.GetLogsByLevel(InfoLevel)
}

func (m *MockLogger) HasErrorWithMessage(msg string) bool {
	errors := m.GetErrorLogs()
	for _, log := range errors {
		if log.Message == msg {
			return true
		}
	}
	return false
}

func (m *MockLogger) Clear() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.logs = make([]LogEntry, 0)
}

func (m *MockLogger) SetLogLevel(level LogLevel) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.logLevel = level
}

func (m *MockLogger) String() string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	var result string
	for _, log := range m.logs {
		levelStr := []string{"DEBUG", "INFO", "WARN", "ERROR", "FATAL"}[log.Level]
		result += fmt.Sprintf("[%s] %s\n", levelStr, log.Message)
	}
	return result
}
