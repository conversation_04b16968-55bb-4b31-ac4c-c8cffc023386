package utils

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/testing/wspkg/mocks"
	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
)

// TestSetup contains all the components needed for testing
type TestSetup struct {
	Manager    *wspkg.Manager
	DB         *mocks.MockQueries
	TokenStore *mocks.MockTokenStore
	Logger     *mocks.MockLogger
	WSCtx      *wspkg.WebSocketCtx
	Echo       *echo.Echo
}

// NewTestSetup creates a complete test setup with all mocked dependencies
func NewTestSetup(t *testing.T) *TestSetup {
	logger := mocks.NewMockLogger()
	db := mocks.NewMockQueries()
	tokenStore := mocks.NewMockTokenStore()

	manager, err := wspkg.DefaultManager()
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}
	manager.SetLogger(logger)

	wsCtx := wspkg.NewWebSocketCtx(manager, db, tokenStore)
	e := echo.New()

	return &TestSetup{
		Manager:    manager,
		DB:         db,
		TokenStore: tokenStore,
		Logger:     logger,
		WSCtx:      wsCtx,
		Echo:       e,
	}
}

// CreateTestWebSocketRequest creates a test HTTP request for WebSocket upgrade
func CreateTestWebSocketRequest(token string) (*http.Request, *httptest.ResponseRecorder) {
	req := httptest.NewRequest(http.MethodGet, "/ws?token="+token, nil)
	req.Header.Set("Connection", "upgrade")
	req.Header.Set("Upgrade", "websocket")
	req.Header.Set("Sec-WebSocket-Version", "13")
	req.Header.Set("Sec-WebSocket-Key", "dGhlIHNhbXBsZSBub25jZQ==")

	rec := httptest.NewRecorder()
	return req, rec
}

// CreateEchoContext creates an Echo context for testing
func CreateEchoContext(req *http.Request, rec *httptest.ResponseRecorder, e *echo.Echo) echo.Context {
	return e.NewContext(req, rec)
}

// WaitForCondition waits for a condition to be true with timeout
func WaitForCondition(t *testing.T, condition func() bool, timeout time.Duration, message string) {
	t.Helper()

	ticker := time.NewTicker(10 * time.Millisecond)
	defer ticker.Stop()

	timeoutCh := time.After(timeout)

	for {
		select {
		case <-ticker.C:
			if condition() {
				return
			}
		case <-timeoutCh:
			t.Fatalf("Timeout waiting for condition: %s", message)
		}
	}
}

// AssertNoErrors checks that no errors were logged
func AssertNoErrors(t *testing.T, logger *mocks.MockLogger) {
	t.Helper()
	errors := logger.GetErrorLogs()
	if len(errors) > 0 {
		t.Errorf("Expected no errors, but got %d errors:", len(errors))
		for _, err := range errors {
			t.Errorf("  - %s", err.Message)
		}
	}
}

// AssertErrorLogged checks that a specific error was logged
func AssertErrorLogged(t *testing.T, logger *mocks.MockLogger, expectedMessage string) {
	t.Helper()
	if !logger.HasErrorWithMessage(expectedMessage) {
		t.Errorf("Expected error message '%s' was not logged", expectedMessage)
		errors := logger.GetErrorLogs()
		t.Errorf("Actual errors logged:")
		for _, err := range errors {
			t.Errorf("  - %s", err.Message)
		}
	}
}

// CreateWebSocketConnection creates a real WebSocket connection for integration tests
func CreateWebSocketConnection(t *testing.T, serverURL string, token string) (*websocket.Conn, *http.Response) {
	t.Helper()

	u, err := url.Parse(serverURL)
	if err != nil {
		t.Fatalf("Failed to parse server URL: %v", err)
	}

	// Convert http to ws
	if u.Scheme == "http" {
		u.Scheme = "ws"
	} else if u.Scheme == "https" {
		u.Scheme = "wss"
	}

	u.Path = "/ws"
	u.RawQuery = "token=" + token

	dialer := websocket.Dialer{
		HandshakeTimeout: 5 * time.Second,
	}

	conn, resp, err := dialer.Dial(u.String(), nil)
	if err != nil {
		t.Fatalf("Failed to connect to WebSocket: %v", err)
	}

	return conn, resp
}

// SendWebSocketMessage sends a message over WebSocket connection
func SendWebSocketMessage(t *testing.T, conn *websocket.Conn, event string, data interface{}) {
	t.Helper()

	message := wspkg.NewMessage(event, []byte(`{}`))
	if data != nil {
		// In a real implementation, you'd marshal the data
		// For now, we'll use a simple string
		if str, ok := data.(string); ok {
			message = wspkg.NewMessage(event, []byte(str))
		}
	}

	err := conn.WriteJSON(message)
	if err != nil {
		t.Fatalf("Failed to send WebSocket message: %v", err)
	}
}

// ReadWebSocketMessage reads a message from WebSocket connection
func ReadWebSocketMessage(t *testing.T, conn *websocket.Conn, timeout time.Duration) *wspkg.Message {
	t.Helper()

	conn.SetReadDeadline(time.Now().Add(timeout))

	var message wspkg.Message
	err := conn.ReadJSON(&message)
	if err != nil {
		t.Fatalf("Failed to read WebSocket message: %v", err)
	}

	return &message
}

// CloseWebSocketConnection safely closes a WebSocket connection
func CloseWebSocketConnection(t *testing.T, conn *websocket.Conn) {
	t.Helper()

	err := conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
	if err != nil {
		t.Logf("Error sending close message: %v", err)
	}

	conn.Close()
}
