# WebSocket Testing Suite

This directory contains comprehensive tests for the new WebSocket implementation (`internal/wspkg`).

## Test Structure

```
testing/
├── README.md                    # This file
├── wspkg/                      # WebSocket package tests
│   ├── integration_test.go     # End-to-end integration tests
│   ├── manager_test.go         # Manager functionality tests
│   ├── client_test.go          # Client functionality tests
│   ├── router_test.go          # Event router tests
│   ├── ws_handlers_test.go     # WebSocket handler tests
│   └── mocks/                  # Mock implementations
│       ├── mock_db.go          # Database mocks
│       ├── mock_tokenstore.go  # Token store mocks
│       └── mock_logger.go      # Logger mocks
├── fixtures/                   # Test data and fixtures
│   ├── test_tokens.json        # Sample tokens for testing
│   └── test_devices.json       # Sample device data
└── utils/                      # Test utilities
    ├── websocket_client.go     # WebSocket test client
    └── test_helpers.go         # Common test helpers
```

## Running Tests

```bash
# Run all WebSocket tests
go test ./testing/wspkg/... -v

# Run specific test suites
go test ./testing/wspkg/ -run TestManager -v
go test ./testing/wspkg/ -run TestClient -v
go test ./testing/wspkg/ -run TestIntegration -v

# Run with race detection
go test ./testing/wspkg/... -race -v

# Run with coverage
go test ./testing/wspkg/... -cover -v
```

## Test Categories

### Unit Tests
- Manager client lifecycle management
- Client connection handling
- Event router functionality
- Message serialization/deserialization

### Integration Tests
- Complete WebSocket connection flow
- Authentication and authorization
- Message routing and handling
- Error scenarios and recovery

### Performance Tests
- Concurrent client connections
- Message throughput
- Memory usage under load
- Connection cleanup efficiency

## Notes

- Tests use mock implementations to avoid dependencies on actual database/services
- Integration tests can be run against a test server instance
- All tests are designed to be deterministic and not rely on external services
