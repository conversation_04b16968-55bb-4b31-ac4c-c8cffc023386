package ws

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
)

type WSRoutes struct {
	e        *echo.Echo
	upgrader *websocket.Upgrader
	hub      *Hub
	query    *database.Queries
	tstore   *utils.TokenStore
}

func NewWSRoute(e *echo.Echo, upgrader *websocket.Upgrader, hub *Hub, query *database.Queries, tstore *utils.TokenStore) *WSRoutes {
	return &WSRoutes{
		e:        e,
		upgrader: upgrader,
		hub:      hub,
		tstore:   tstore,
		query:    query,
	}
}

func (W *WSRoutes) ClientConnectingHandler(c echo.Context) error {
	log.Print("[WSEND] >> Client Handler")
	token := c.QueryParam("token")
	tokenInfo, isValid := W.tstore.ValidateToken(token)
	log.Printf("[WSEND] >> Client Handler Params: %v", token)
	if !isValid {
		return c.String(http.StatusUnauthorized, "token access issue")
	}

	W.tstore.RemoveToken(token)

	clientMeta, err := SelectClientType(W.query, tokenInfo.ConnType)
	if err != nil {
		return c.String(http.StatusBadRequest, "invalid client access || connection type is invalid")
	}

	conn, err := W.upgrader.Upgrade(c.Response().Writer, c.Request(), nil)
	log.Print("[WSEND] >> WS Upgraded")
	log.Println("WebSocket ***")
	if err != nil {
		log.Printf("[WSEND] >> WS Upgraded Error: %v", err)
		return c.String(http.StatusInternalServerError, "ws upgrader error")
	}

	wsConfig := types.ConnMeta{
		PingTime:  (540 * time.Second) / 10,
		PongTime:  60 * time.Second,
		WriteWait: 10 * time.Second,
	}
	wsclient := NewClient(
		tokenInfo.ClientId,
		tokenInfo.BranchId,
		tokenInfo.ConnType,
		conn,
		clientMeta,
		&wsConfig,
	)
	W.hub.clientJoin <- wsclient

	conn.SetCloseHandler(func(code int, text string) error {
		log.Printf("[INFO] Client disconnected: %s, Code: %d, Text: %s", wsclient.id, code, text)
		wsclient.SafeDisconnect(W)
		return nil
	})

	clientId64, err := strconv.ParseInt(tokenInfo.ClientId, 10, 64)
	if err != nil {
		return c.String(http.StatusBadRequest, "invalid client id")
	}

	err = W.query.UpdateDoorOnlineStatus(c.Request().Context(), database.UpdateDoorOnlineStatusParams{
		IsOnline: true,
		ID:       clientId64,
	})
	if err != nil {
		return c.String(http.StatusInternalServerError, "failed to update online status")
	}

	go wsclient.ReadPipe(W)
	go wsclient.WritePipe(W)
	fmt.Print("[WSEND] >> Client Handler: 56866465", wsclient.id)

	return nil
}

func (W *WSRoutes) QRClientConnectingHandler(c echo.Context) error {
	token := c.QueryParam("token")
	tokenInfo, isValid := W.tstore.ValidateToken(token)
	log.Printf("[WSEND] >> Client Handler Params: %v", token)
	if !isValid {
		return c.String(http.StatusUnauthorized, "token access issue")
	}

	W.tstore.RemoveToken(token)

	clientMeta, err := SelectClientType(W.query, tokenInfo.ConnType)
	if err != nil {
		return c.String(http.StatusBadRequest, "invalid client access || connection type is invalid")
	}

	clientMetaStruct, ok := clientMeta.(*QRConnType)
	if !ok {
		return c.String(http.StatusBadRequest, "invalid client access || connection type is invalid")
	}

	clientMetaStruct.SetReqDevice(tokenInfo.ReqId)

	conn, err := W.upgrader.Upgrade(c.Response().Writer, c.Request(), nil)
	log.Print("[WSEND] >> WS Upgraded")
	if err != nil {
		return c.String(http.StatusInternalServerError, "ws upgrader error")
	}

	// add qr device to door lock
	doorLockClient, ok := W.hub.GetClient(tokenInfo.ReqId)
	if !ok {
		return c.String(http.StatusBadRequest, "door lock not found")
	}
	if doorLockClient.isBanned {
		return c.String(http.StatusUnauthorized, "door lock is banned")
	}
	// Use thread-safe method to add QR device
	doorLockClient.Data.(*DoorLockType).AddQRDevice(DoorLockQRType{
		QRType: tokenInfo.ConnType,
		Id:     tokenInfo.ClientId,
	})

	wsConfig := types.ConnMeta{
		PingTime:  (540 * time.Second) / 10,
		PongTime:  60 * time.Second,
		WriteWait: 10 * time.Second,
	}
	wsclient := NewClient(
		tokenInfo.ClientId,
		tokenInfo.BranchId,
		tokenInfo.ConnType,
		conn,
		clientMeta,
		&wsConfig,
	)
	W.hub.clientJoin <- wsclient

	conn.SetCloseHandler(func(code int, text string) error {
		log.Printf("[INFO] QR Client disconnected: %s, Code: %d, Text: %s", wsclient.id, code, text)
		wsclient.SafeDisconnect(W)
		return nil
	})

	go wsclient.ReadPipe(W)
	go wsclient.WritePipe(W)

	return nil
}

func (W *WSRoutes) AuthenticateQRClient(c echo.Context) error {
	var conntypereq types.QRConnectReq
	err := c.Bind(&conntypereq)
	if err != nil {
		log.Print("[WSEND] >> Bind Error")
		return c.String(http.StatusBadRequest, "the request json invalid")
	}
	checkDevice, ok := W.hub.GetClient(conntypereq.ReqDeviceId)
	if !ok {
		return c.String(http.StatusBadRequest, "device not found")
	}
	if checkDevice.isBanned {
		return c.String(http.StatusUnauthorized, "device is banned")
	}
	// check is there already qr device that connected
	qrDevices := checkDevice.Data.(*DoorLockType).GetQRDevices()
	for _, qrId := range qrDevices {
		qrDevice, ok := W.hub.GetClient(qrId.Id)
		if ok {
			if qrDevice.typeName == conntypereq.ConnType {
				if qrDevice.isBanned {
					return c.String(http.StatusUnauthorized, "device is banned")
				}
			}
		}
	}

	// Generate token
	token := utils.GenerateSecureToken()

	// Generate UUID for QR client - this will be the actual client ID
	qrClientId := uuid.New().String()

	// Store token with 2-minute expiration
	W.tstore.StoreToken(
		token,
		qrClientId,
		conntypereq.ConnType,
		conntypereq.BranchId,
		conntypereq.ReqDeviceId,
		"",
		time.Now().Add(2*time.Minute),
	)

	// Return both token and the client ID that will be assigned
	return c.JSON(http.StatusOK, map[string]string{
		"token":    token,
		"clientId": qrClientId,
	})
}

func (W *WSRoutes) AuthenticateClient(c echo.Context) error {
	// ... existing authentication code ...
	var conntypereq types.WSConnectReq
	err := c.Bind(&conntypereq)
	if err != nil {
		log.Print("[WSEND] >> Bind Error")
		return c.String(http.StatusBadRequest, "the request json invalid")
	}
	checkData := database.ValidateDeviceDetailsParams{
		ID:       int64(conntypereq.Id),
		ApiKey:   utils.HashAPIKey(conntypereq.APIKey),
		BranchID: int64(conntypereq.BranchId),
	}
	log.Print(checkData)
	isValid, err := W.query.ValidateDeviceDetails(c.Request().Context(), checkData)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, "Api key error")
	}
	if len(isValid) <= 0 {
		log.Print(isValid)
		return c.JSON(http.StatusUnauthorized, "Api key not valid with credintial")
	}

	// checkApi, err := utils.ValidateAPIKey(W.query, int64(conntypereq.Id), conntypereq.APIKey)
	// if err != nil {
	// 	return c.JSON(http.StatusUnauthorized, "Api key error")
	// }
	// if !checkApi {
	// 	return c.JSON(http.StatusUnauthorized, "Api key not valid")
	// }

	// Generate token
	token := utils.GenerateSecureToken()

	// Store token with 2-minute expiration
	W.tstore.StoreToken(
		token,
		strconv.Itoa(conntypereq.Id),
		conntypereq.ConnType,
		conntypereq.BranchId,
		"",
		conntypereq.APIKey,
		time.Now().Add(2*time.Minute),
	)

	return c.JSON(http.StatusOK, map[string]string{"token": token})
}

func (W *WSRoutes) RemoveClient(c echo.Context) error {
	var req types.DisconnectDoorClinetType
	err := c.Bind(&req)
	if err != nil {
		return c.JSON(http.StatusBadRequest, "request is not valid")
	}

	client, exits := W.hub.GetClient(req.ClientId)
	if !exits {
		return c.JSON(http.StatusBadRequest, "client not found")
	}

	client.SafeDisconnect(W)
	return c.JSON(http.StatusOK, "client disconnected successfully")
}

func (W *WSRoutes) ClearHub(c echo.Context) error {
	var req types.SSforClean
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, "request is not valid")
	}
	if req.Code != 119911 {
		return c.JSON(http.StatusForbidden, "be careful 999")
	}

	if req.BranchId != 1 {
		return c.JSON(http.StatusBadRequest, "i know the place")
	}

	W.hub.ClearAllClients()

	return c.JSON(http.StatusOK, "all clients disconnected successfully")
}

func SelectClientType(db *database.Queries, connType string) (ClientMeta, error) {
	log.Printf("[WSEND] >> Client selection fn %v", connType)
	switch connType {
	case "door-lock":
		log.Print("[WSEND] >> Door-lock")
		log.Print("[WSEND] >> DoorLock - Auth Success")
		return &DoorLockType{
			IsOpen: false,
		}, nil
	case "qr-in":
		log.Print("[QRMETACreate] >> qr-in")
		return &QRConnType{
			QRType: "in",
			Update: time.Now(),
		}, nil

	case "qr-out":
		log.Print("[QRMETACreate] >> qr-in")
		return &QRConnType{
			QRType: "out",
			Update: time.Now(),
		}, nil
	default:
		return nil, errors.New("invalid client access")

	}
}
