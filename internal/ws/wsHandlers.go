package ws

import (
	"encoding/json"
	"errors"
	"log"
	"sync"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
)

type DoorLockQRType struct {
	QRType string `json:"qrType"`
	Id     string `json:"id"`
}

type DoorLockType struct {
	mu           sync.RWMutex     `json:"-"`
	IsOpen       bool             `json:"isOpen"`
	QRDevices    []DoorLockQRType `json:"qrDevices"`
	LockTime     time.Time        `json:"lockTime"`
	ProcessingQR string           `json:"processingQR"`
}

func (D *DoorLockType) RemoveQRDevice(qrID string) {
	D.mu.Lock()
	defer D.mu.Unlock()

	for i, qr := range D.QRDevices {
		if qr.Id == qrID {
			D.QRDevices = append(D.QRDevices[:i], D.QRDevices[i+1:]...)
			return // Exit after removing the first match
		}
	}
}

func (D *DoorLockType) CheckIsOpen(client *Client) bool {
	D.mu.RLock()
	defer D.mu.RUnlock()
	return D.IsOpen
}

func (D *DoorLockType) GetQRDevices() []DoorLockQRType {
	D.mu.RLock()
	defer D.mu.RUnlock()
	// Return a copy to prevent external modifications
	devices := make([]DoorLockQRType, len(D.QRDevices))
	copy(devices, D.QRDevices)
	return devices
}

func (D *DoorLockType) SetProcessingQR(qr string) {
	D.mu.Lock()
	defer D.mu.Unlock()
	D.ProcessingQR = qr
}

// AddQRDevice safely adds a QR device to the door lock
func (D *DoorLockType) AddQRDevice(device DoorLockQRType) {
	D.mu.Lock()
	defer D.mu.Unlock()
	D.QRDevices = append(D.QRDevices, device)
}

// SetIsOpen safely sets the door open state
func (D *DoorLockType) SetIsOpen(isOpen bool) {
	D.mu.Lock()
	defer D.mu.Unlock()
	D.IsOpen = isOpen
}

// SetLockTime safely sets the lock time
func (D *DoorLockType) SetLockTime(lockTime time.Time) {
	D.mu.Lock()
	defer D.mu.Unlock()
	D.LockTime = lockTime
}

// GetProcessingQR safely gets the processing QR
func (D *DoorLockType) GetProcessingQR() string {
	D.mu.RLock()
	defer D.mu.RUnlock()
	return D.ProcessingQR
}

func (D *DoorLockType) EventHandler(ctx *MessageContext) error {
	switch ctx.Message.MsgType {
	case "isunlock":
		var data types.IsUnlockMsg
		if err := json.Unmarshal(ctx.Message.Data, &data); err != nil {
			return errors.New("data type parsing issue")
		}

		// Use thread-safe methods to update door state
		D.SetIsOpen(data.IsUnlock)
		D.SetLockTime(data.Timestamp)

		log.Printf("[DoorLock] >> set %v", ctx.Client)
		if ctx.Client.GetProcessing() {
			if ctx.Client.GetStatus() == code.WAITINGFORUNLOCK {
				if !data.IsUnlock {
					ctx.Client.SetProcessing(false)
					// Get QR devices safely
					qrDevices := D.GetQRDevices()
					for _, qrClient := range qrDevices {
						ctx.Ws.hub.SendToClient(qrClient.Id, types.MessageType{
							MsgType: "unlock-attempt-success",
						})
					}
					// Get processing QR safely
					processingQR := D.GetProcessingQR()
					err := ctx.Ws.hub.SendToClient(processingQR, types.MessageType{
						MsgType: "generate-new",
					})
					if err != nil {
						log.Printf("[DoorLock] >> %v", err)
					}
				}
			}
		}
	case "unlock":
		var data types.IsUnlockMsg
		if err := json.Unmarshal(ctx.Message.Data, &data); err != nil {
			return errors.New("data type parsing issue")
		}
		if data.IsUnlock {
			ctx.Client.WriteToConnText(types.SendWSMsg{
				Code: code.DOORUNLOCKED,
				Msg:  "door unlocked",
			})
			ctx.Client.SetStatus(code.WAITINGFORUNLOCK)
		}

	case "unlock-attempt-success":
		ctx.Client.SetProcessing(false)
		ctx.Client.SetStatus(code.DOORLOCKED)
		err := ctx.Client.WriteToConnText(types.SendWSMsg{
			Code: code.SUCCESSATTEMPT,
			Msg:  "qr being processed",
		})
		if err != nil {
			log.Printf("[QRHandler] >> %v", err)
		}
		// Get processing QR safely
		processingQR := D.GetProcessingQR()
		err = ctx.Ws.hub.SendToClient(processingQR, types.SendWSMsg{
			Code: code.SUCCESSATTEMPT,
			Msg:  "user accessed successfull.",
		})
		if err != nil {
			log.Printf("[DoorLock] >> %v", err)
		}

	case "processError":
		ctx.Client.SetProcessing(false)
		ctx.Client.SetStatus(code.IDEL)
		err := ctx.Client.WriteToConnText(types.SendWSMsg{
			Code: code.ERRORPROCESSING,
		})
		if err != nil {
			log.Printf("[QRHandler] >> %v", err)
		}

	case "connect-id":
		ctx.Client.SetProcessing(true)
		ctx.Client.WriteToConnText(types.SendWSMsg{
			Code: code.CONNECTID,
			Msg:  ctx.Client.id,
		})
		ctx.Client.SetProcessing(false)
	default:
		return nil
	}
	return nil
}

type QRConnType struct {
	QRType    string    `json:"qrType"`
	ReqDevice string    `json:"reqDevice"`
	Update    time.Time `json:"update"`
}

func (Q *QRConnType) SetReqDevice(device string) {
	Q.ReqDevice = device
}

func (Q *QRConnType) EventHandler(ctx *MessageContext) error {
	switch ctx.Message.MsgType {
	case "generate-new":
		log.Printf("[QRHandler] >> set %v", ctx.Client.Data)
		qdata := utils.QRCodeInfo{
			ReqDeviceId: Q.ReqDevice,
			BranchId:    ctx.Client.branchID,
			ClientId:    ctx.Client.id,
			ConnType:    ctx.Client.typeName,
			GenTime:     time.Now(),
			ExpTime:     time.Now().Add(time.Minute * 10),
		}

		token, err := utils.GenerateTokenForToday(qdata)
		if err != nil {
			return err
		}
		ctx.Client.WriteToConnText(types.SendWSMsg{
			Code: code.QRGENERATE,
			Msg:  token,
		})

	case "processing":
		log.Printf("[QRHandler] >> processing %v", ctx.Client.id)
		// var data types.ProcessingEventMsg
		// if err := json.Unmarshal(ctx.Message.Data, &data); err != nil {
		// 	return errors.New("data type parsing issue")
		// }
		ctx.Client.SetProcessing(true)
		ctx.Client.SetStatus(code.QRPROCESSING)
		err := ctx.Client.WriteToConnText(types.SendWSMsg{
			Code: code.QRPROCESSING,
			Msg:  "qr being processed",
		})
		if err != nil {
			log.Printf("[QRHandler] >> %v", err)
		}

	case "unlock-attempt-success":
		log.Printf("[QRHandler] >> processing")
		ctx.Client.SetProcessing(false)
		ctx.Client.SetStatus(code.IDEL)
		err := ctx.Client.WriteToConnText(types.SendWSMsg{
			Code: code.SUCCESSATTEMPT,
			Msg:  "qr being processed",
		})
		if err != nil {
			log.Printf("[QRHandler] >> %v", err)
		}

	case "set-idel":
		ctx.Client.SetProcessing(false)
		ctx.Client.SetStatus(code.IDEL)

		err := ctx.Client.WriteToConnText(types.SendWSMsg{
			Code: code.IDEL,
			Msg:  "qr being processed",
		})
		if err != nil {
			log.Printf("[QRHandler] >> %v", err)
		}

	default:
		return nil
	}
	return nil
}
