package ws

import (
	"sync"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
)

// TestSendToClientErrorHandling tests various error scenarios in SendToClient
func TestSendToClientErrorHandling(t *testing.T) {
	hub := NewHub()

	t.Run("SendToClient_NonExistentClient", func(t *testing.T) {
		err := hub.SendToClient("non-existent", map[string]string{"test": "message"})
		if err == nil {
			t.Error("Expected error for non-existent client, got nil")
		}
		if err.Error() != "client not found" {
			t.Errorf("Expected 'client not found' error, got: %v", err)
		}
	})

	t.Run("SendToClient_BannedClient", func(t *testing.T) {
		// Create a banned client
		connMeta := &types.ConnMeta{
			WriteWait: 10 * time.Second,
			PongTime:  60 * time.Second,
			PingTime:  54 * time.Second,
		}

		doorLock := &DoorLockType{}
		client := NewClient("banned-client", 1, "door-lock", nil, doorLock, connMeta)
		client.isBanned = true

		hub.clients["banned-client"] = client

		err := hub.SendToClient("banned-client", map[string]string{"test": "message"})
		if err == nil {
			t.Error("Expected error for banned client, got nil")
		}
		if err.Error() != "client is banned" {
			t.Errorf("Expected 'client is banned' error, got: %v", err)
		}
	})

	t.Run("SendToClient_InvalidJSON", func(t *testing.T) {
		// Create a client
		connMeta := &types.ConnMeta{
			WriteWait: 10 * time.Second,
			PongTime:  60 * time.Second,
			PingTime:  54 * time.Second,
		}

		doorLock := &DoorLockType{}
		client := NewClient("test-client", 1, "door-lock", nil, doorLock, connMeta)
		hub.clients["test-client"] = client

		// Try to send a message that can't be marshaled to JSON
		invalidMessage := make(chan int) // channels can't be marshaled to JSON

		err := hub.SendToClient("test-client", invalidMessage)
		if err == nil {
			t.Error("Expected JSON marshaling error, got nil")
		}
	})

	t.Run("SendToClient_FullBuffer", func(t *testing.T) {
		// Create a client with a small buffer
		connMeta := &types.ConnMeta{
			WriteWait: 10 * time.Second,
			PongTime:  60 * time.Second,
			PingTime:  54 * time.Second,
		}

		doorLock := &DoorLockType{}
		client := NewClient("buffer-test", 1, "door-lock", nil, doorLock, connMeta)

		// Fill the buffer by not reading from it
		for i := 0; i < cap(client.send); i++ {
			client.send <- []byte("test")
		}

		hub.clients["buffer-test"] = client

		err := hub.SendToClient("buffer-test", map[string]string{"test": "message"})
		if err == nil {
			t.Error("Expected buffer full error, got nil")
		}
		if err.Error() != "client message buffer full or send timeout" {
			t.Errorf("Expected buffer full error, got: %v", err)
		}
	})

	t.Run("SendToClient_ConnectionHealthCheck", func(t *testing.T) {
		// Test the FIXED connection health check

		connMeta := &types.ConnMeta{
			WriteWait: 10 * time.Second,
			PongTime:  60 * time.Second,
			PingTime:  54 * time.Second,
		}

		doorLock := &DoorLockType{}
		client := NewClient("health-test", 1, "door-lock", nil, doorLock, connMeta)

		// Simulate a closed connection by setting conn to nil
		client.conn = nil

		hub.clients["health-test"] = client

		// FIXED implementation now checks connection health before sending
		err := hub.SendToClient("health-test", map[string]string{"test": "message"})

		if err == nil {
			t.Error("Expected error for closed connection, got nil")
		}
		if err.Error() != "client connection is closed" {
			t.Errorf("Expected 'client connection is closed' error, got: %v", err)
		}
		t.Logf("✅ FIXED: SendToClient now properly detects closed connections")
		t.Logf("   Error returned: %v", err)
	})
}

// TestConcurrentSendToClient tests race conditions in SendToClient
func TestConcurrentSendToClient(t *testing.T) {
	hub := NewHub()

	connMeta := &types.ConnMeta{
		WriteWait: 10 * time.Second,
		PongTime:  60 * time.Second,
		PingTime:  54 * time.Second,
	}

	doorLock := &DoorLockType{}
	client := NewClient("concurrent-test", 1, "door-lock", nil, doorLock, connMeta)
	hub.clients["concurrent-test"] = client

	// Start a goroutine to consume messages from the send channel
	go func() {
		for range client.send {
			// Consume messages to prevent buffer from filling
		}
	}()

	var wg sync.WaitGroup
	numGoroutines := 50

	// Test concurrent sends
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < 10; j++ {
				err := hub.SendToClient("concurrent-test", map[string]interface{}{
					"id":      id,
					"message": j,
				})
				if err != nil {
					t.Errorf("Unexpected error in concurrent send: %v", err)
				}
			}
		}(i)
	}

	wg.Wait()
	t.Log("✅ Concurrent SendToClient test completed")
}

// TestBroadcastToBranchErrorHandling tests error scenarios in BroadcastToBranch
func TestBroadcastToBranchErrorHandling(t *testing.T) {
	hub := NewHub()

	t.Run("BroadcastToBranch_NoBranchClients", func(t *testing.T) {
		err := hub.BroadcastToBranch(999, map[string]string{"test": "message"})
		if err == nil {
			t.Error("Expected error for non-existent branch, got nil")
		}
		if err.Error() != "no clients found in the specified branch" {
			t.Errorf("Expected 'no clients found' error, got: %v", err)
		}
	})

	t.Run("BroadcastToBranch_InvalidJSON", func(t *testing.T) {
		// Add a client to branch 1
		connMeta := &types.ConnMeta{
			WriteWait: 10 * time.Second,
			PongTime:  60 * time.Second,
			PingTime:  54 * time.Second,
		}

		doorLock := &DoorLockType{}
		client := NewClient("branch-test", 1, "door-lock", nil, doorLock, connMeta)
		hub.clients["branch-test"] = client

		// Try to broadcast invalid JSON
		invalidMessage := make(chan int)

		err := hub.BroadcastToBranch(1, invalidMessage)
		if err == nil {
			t.Error("Expected JSON marshaling error, got nil")
		}
	})

	t.Run("BroadcastToBranch_PartialFailure", func(t *testing.T) {
		// Create multiple clients in the same branch
		connMeta := &types.ConnMeta{
			WriteWait: 10 * time.Second,
			PongTime:  60 * time.Second,
			PingTime:  54 * time.Second,
		}

		doorLock := &DoorLockType{}

		// Client 1: Normal client
		client1 := NewClient("branch-client-1", 2, "door-lock", nil, doorLock, connMeta)
		hub.clients["branch-client-1"] = client1

		// Client 2: Client with full buffer
		client2 := NewClient("branch-client-2", 2, "door-lock", nil, doorLock, connMeta)
		// Fill the buffer
		for i := 0; i < cap(client2.send); i++ {
			client2.send <- []byte("test")
		}
		hub.clients["branch-client-2"] = client2

		// Start consuming from client1 to prevent its buffer from filling
		go func() {
			for range client1.send {
				// Consume messages
			}
		}()

		// Broadcast should succeed for client1 but fail for client2
		err := hub.BroadcastToBranch(2, map[string]string{"test": "broadcast"})

		// Current implementation doesn't return error for partial failures
		// This is a design issue - should we fail the entire broadcast or continue?
		if err != nil {
			t.Errorf("Unexpected error in broadcast: %v", err)
		}

		t.Log("✅ BroadcastToBranch handles partial failures by skipping failed clients")
	})
}
