package ws

import (
	"sync"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
)

// MockClientMeta implements ClientMeta interface for testing
type MockClientMeta struct {
	mu sync.Mutex
}

func (m *MockClientMeta) EventHandler(ctx *MessageContext) error {
	return nil
}

// TestClientProcessingRaceCondition demonstrates race condition in processing flag
func TestClientProcessingRaceCondition(t *testing.T) {
	// Create a mock client
	connMeta := &types.ConnMeta{
		WriteWait: 10 * time.Second,
		PongTime:  60 * time.Second,
		PingTime:  54 * time.Second,
	}

	client := NewClient("test-client", 1, "door-lock", nil, &MockClientMeta{}, connMeta)

	// Number of goroutines to simulate concurrent access
	numGoroutines := 100
	iterations := 1000

	var wg sync.WaitGroup

	// Start multiple goroutines that concurrently access processing flag
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < iterations; j++ {
				// Simulate concurrent read/write operations
				if id%2 == 0 {
					client.SetProcessing(true)
					client.SetStatus(code.QRPROCESSING)
				} else {
					_ = client.GetProcessing()
					_ = client.GetStatus()
				}
			}
		}(i)
	}

	wg.Wait()

	// This test primarily demonstrates that the current implementation
	// has proper mutex protection for client state
	t.Log("Client processing race condition test completed")
}

// TestDoorLockIsOpenRaceCondition demonstrates race condition in DoorLockType.IsOpen
func TestDoorLockIsOpenRaceCondition(t *testing.T) {
	doorLock := &DoorLockType{
		IsOpen:    false,
		QRDevices: []DoorLockQRType{},
		LockTime:  time.Now(),
	}

	connMeta := &types.ConnMeta{
		WriteWait: 10 * time.Second,
		PongTime:  60 * time.Second,
		PingTime:  54 * time.Second,
	}

	client := NewClient("test-door", 1, "door-lock", nil, doorLock, connMeta)

	numGoroutines := 50
	iterations := 1000

	var wg sync.WaitGroup
	raceDetected := false

	// Start multiple goroutines that concurrently access IsOpen field
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < iterations; j++ {
				if id%2 == 0 {
					// Writer goroutine - modifies IsOpen without proper synchronization
					doorLock.IsOpen = !doorLock.IsOpen
				} else {
					// Reader goroutine - reads IsOpen
					_ = doorLock.CheckIsOpen(client)
				}
			}
		}(i)
	}

	wg.Wait()

	if raceDetected {
		t.Error("Race condition detected in DoorLockType.IsOpen access")
	}

	t.Log("DoorLock IsOpen race condition test completed")
}

// TestQRDevicesSliceRaceCondition demonstrates race condition in QRDevices slice
func TestQRDevicesSliceRaceCondition(t *testing.T) {
	doorLock := &DoorLockType{
		IsOpen:    false,
		QRDevices: []DoorLockQRType{},
		LockTime:  time.Now(),
	}

	numGoroutines := 20
	iterations := 100

	var wg sync.WaitGroup

	// Start multiple goroutines that concurrently modify QRDevices slice
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < iterations; j++ {
				if id%2 == 0 {
					// Add QR device using unsafe direct access (to demonstrate race condition)
					doorLock.QRDevices = append(doorLock.QRDevices, DoorLockQRType{
						QRType: "in",
						Id:     string(rune('A' + id + j)),
					})
				} else {
					// Remove QR device using unsafe direct access
					if len(doorLock.QRDevices) > 0 {
						doorLock.RemoveQRDevice(doorLock.QRDevices[0].Id)
					}
				}
			}
		}(i)
	}

	wg.Wait()

	t.Log("QRDevices slice race condition test completed")
	t.Logf("Final QRDevices count: %d", len(doorLock.GetQRDevices()))
}

// TestConcurrentClientStateChanges tests concurrent state changes across different operations
func TestConcurrentClientStateChanges(t *testing.T) {
	doorLock := &DoorLockType{
		IsOpen:    false,
		QRDevices: []DoorLockQRType{},
		LockTime:  time.Now(),
	}

	connMeta := &types.ConnMeta{
		WriteWait: 10 * time.Second,
		PongTime:  60 * time.Second,
		PingTime:  54 * time.Second,
	}

	client := NewClient("test-client", 1, "door-lock", nil, doorLock, connMeta)

	var wg sync.WaitGroup
	numGoroutines := 10
	iterations := 500

	// Simulate concurrent operations that would happen in real usage
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < iterations; j++ {
				switch id % 4 {
				case 0:
					// Simulate door unlock process
					client.SetProcessing(true)
					client.SetStatus(code.QRPROCESSING)
					doorLock.SetIsOpen(true)
					client.SetProcessing(false)
					client.SetStatus(code.DOORUNLOCKED)
				case 1:
					// Simulate door lock process
					doorLock.SetIsOpen(false)
					client.SetStatus(code.DOORLOCKED)
				case 2:
					// Simulate QR device operations
					qrDevice := DoorLockQRType{QRType: "in", Id: "qr-test"}
					doorLock.AddQRDevice(qrDevice)
				case 3:
					// Simulate reading operations
					_ = client.GetProcessing()
					_ = client.GetStatus()
					_ = doorLock.CheckIsOpen(client)
					_ = doorLock.GetQRDevices()
				}
			}
		}(i)
	}

	wg.Wait()

	t.Log("Concurrent client state changes test completed")
}

// TestThreadSafeDoorLockOperations tests that the fixed implementation is thread-safe
func TestThreadSafeDoorLockOperations(t *testing.T) {
	doorLock := &DoorLockType{
		IsOpen:    false,
		QRDevices: []DoorLockQRType{},
		LockTime:  time.Now(),
	}

	connMeta := &types.ConnMeta{
		WriteWait: 10 * time.Second,
		PongTime:  60 * time.Second,
		PingTime:  54 * time.Second,
	}

	client := NewClient("test-client", 1, "door-lock", nil, doorLock, connMeta)

	numGoroutines := 50
	iterations := 1000

	var wg sync.WaitGroup

	// Test concurrent operations using thread-safe methods
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < iterations; j++ {
				switch id % 5 {
				case 0:
					// Test door state operations
					doorLock.SetIsOpen(true)
					_ = doorLock.CheckIsOpen(client)
					doorLock.SetIsOpen(false)
				case 1:
					// Test QR device operations
					qrDevice := DoorLockQRType{
						QRType: "in",
						Id:     "test-qr-" + string(rune('A'+id)) + "-" + string(rune('0'+j%10)),
					}
					doorLock.AddQRDevice(qrDevice)
				case 2:
					// Test QR device removal
					devices := doorLock.GetQRDevices()
					if len(devices) > 0 {
						doorLock.RemoveQRDevice(devices[0].Id)
					}
				case 3:
					// Test processing QR operations
					doorLock.SetProcessingQR("test-processing-" + string(rune('A'+id)))
					_ = doorLock.GetProcessingQR()
				case 4:
					// Test lock time operations
					doorLock.SetLockTime(time.Now())
					_ = doorLock.GetQRDevices()
				}
			}
		}(i)
	}

	wg.Wait()

	// Verify final state is consistent
	devices := doorLock.GetQRDevices()
	processingQR := doorLock.GetProcessingQR()
	isOpen := doorLock.CheckIsOpen(client)

	t.Log("Thread-safe operations test completed successfully")
	t.Logf("Final state - Devices: %d, ProcessingQR: %s, IsOpen: %v",
		len(devices), processingQR, isOpen)
}

// TestNoRaceConditionsAfterFix verifies that race conditions are eliminated
func TestNoRaceConditionsAfterFix(t *testing.T) {
	doorLock := &DoorLockType{
		IsOpen:    false,
		QRDevices: []DoorLockQRType{},
		LockTime:  time.Now(),
	}

	connMeta := &types.ConnMeta{
		WriteWait: 10 * time.Second,
		PongTime:  60 * time.Second,
		PingTime:  54 * time.Second,
	}

	client := NewClient("test-client", 1, "door-lock", nil, doorLock, connMeta)

	var wg sync.WaitGroup
	numGoroutines := 100
	iterations := 500

	// This test should pass without race conditions when run with -race flag
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < iterations; j++ {
				// All operations use thread-safe methods
				doorLock.SetIsOpen(id%2 == 0)
				doorLock.AddQRDevice(DoorLockQRType{
					QRType: "test",
					Id:     "device-" + string(rune('A'+id%26)) + "-" + string(rune('0'+j%10)),
				})
				_ = doorLock.CheckIsOpen(client)
				devices := doorLock.GetQRDevices()
				if len(devices) > 0 {
					doorLock.RemoveQRDevice(devices[len(devices)-1].Id)
				}
				doorLock.SetProcessingQR("processing-" + string(rune('A'+id%26)))
				_ = doorLock.GetProcessingQR()
				doorLock.SetLockTime(time.Now())
			}
		}(i)
	}

	wg.Wait()

	t.Log("No race conditions test completed - all operations were thread-safe")
}
