package ws

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strconv"
	"sync"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/gorilla/websocket"
)

type MessageContext struct {
	Client  *Client
	Ws      *WSRoutes
	Message types.MessageType
}

type ClientMeta interface {
	EventHandler(ctx *MessageContext) error
}

type Client struct {
	id         string
	branchID   int
	typeName   string
	isBanned   bool
	conn       *websocket.Conn
	send       chan []byte
	Data       ClientMeta
	connMeta   *types.ConnMeta
	status     int
	processing bool
	Mut        sync.RWMutex
}

func NewClient(id string, branchID int, typeName string, conn *websocket.Conn, meta ClientMeta, connMeta *types.ConnMeta) *Client {
	return &Client{
		id:       id,
		typeName: typeName,
		branchID: branchID,
		isBanned: false,
		conn:     conn,
		send:     make(chan []byte, 100),
		Data:     meta,
		status:   code.IDEL,
		connMeta: connMeta,
	}
}

func (c *Client) SetProcessing(set bool) {
	c.Mut.Lock()
	defer c.Mut.Unlock()
	c.processing = set
}

func (c *Client) GetProcessing() bool {
	c.Mut.RLock()
	defer c.Mut.RUnlock()
	return c.processing
}

func (c *Client) SetStatus(status int) {
	c.Mut.Lock()
	defer c.Mut.Unlock()
	c.status = status
}

func (c *Client) GetStatus() int {
	c.Mut.RLock()
	defer c.Mut.RUnlock()
	return c.status
}

func (c *Client) IsBanned() bool {
	c.Mut.RLock()
	defer c.Mut.RUnlock()
	return c.isBanned
}

// ClientState represents the complete state of a client
type ClientState struct {
	Processing bool
	Status     int
}

// GetState returns the current complete state atomically
func (c *Client) GetState() ClientState {
	c.Mut.RLock()
	defer c.Mut.RUnlock()
	return ClientState{
		Processing: c.processing,
		Status:     c.status,
	}
}

// SetStateAtomic sets both processing and status atomically
func (c *Client) SetStateAtomic(processing bool, status int) error {
	c.Mut.Lock()
	defer c.Mut.Unlock()

	// Validate state transition
	if err := c.validateStateTransition(c.processing, c.status, processing, status); err != nil {
		return err
	}

	c.processing = processing
	c.status = status
	return nil
}

// validateStateTransition checks if a state transition is valid
func (c *Client) validateStateTransition(oldProcessing bool, oldStatus int, newProcessing bool, newStatus int) error {
	// Define valid state combinations
	validStates := map[bool]map[int]bool{
		false: { // Not processing
			code.IDEL:               true,
			code.DOORLOCKED:         true,
			code.DOORUNLOCKED:       true,
			code.CLIENTDISCONNECTED: true,
		},
		true: { // Processing
			code.QRPROCESSING:     true,
			code.WAITINGFORUNLOCK: true,
		},
	}

	// Check if new state is valid
	if statusMap, exists := validStates[newProcessing]; !exists {
		return fmt.Errorf("invalid processing state: %v", newProcessing)
	} else if !statusMap[newStatus] {
		return fmt.Errorf("invalid status %d for processing=%v", newStatus, newProcessing)
	}

	// Check for invalid transitions
	if oldProcessing && newProcessing && oldStatus != newStatus {
		// Allow status changes while processing (e.g., QRPROCESSING -> WAITINGFORUNLOCK)
		return nil
	}

	return nil
}

// TransitionToProcessing atomically transitions to processing state
func (c *Client) TransitionToProcessing(status int) error {
	return c.SetStateAtomic(true, status)
}

// TransitionToIdle atomically transitions to idle state
func (c *Client) TransitionToIdle() error {
	return c.SetStateAtomic(false, code.IDEL)
}

// IsStateConsistent checks if the current state is consistent
func (c *Client) IsStateConsistent() bool {
	state := c.GetState()
	err := c.validateStateTransition(state.Processing, state.Status, state.Processing, state.Status)
	return err == nil
}

func (c *Client) Close() {
	c.Mut.Lock()
	defer c.Mut.Unlock()

	// Already closed check - idempotent operation
	if c.conn == nil && c.send == nil {
		return
	}

	// Close connection safely
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	// Close channel safely with panic protection
	if c.send != nil {
		// Use defer to ensure channel is set to nil even if close panics
		defer func() {
			c.send = nil
		}()

		// Drain channel before closing to prevent goroutine blocks
		go func() {
			for range c.send {
				// Drain remaining messages
			}
		}()

		close(c.send)
	}
}

func (c *Client) SafeDisconnect(wsR *WSRoutes) {
	// Use a lock to ensure thread safety during disconnection
	c.Mut.Lock()
	defer c.Mut.Unlock()

	// Check if already closed to avoid double-closing
	if c.conn == nil && c.send == nil {
		return
	}

	// Log the disconnection
	log.Printf("[INFO] Client %s disconnecting safely", c.id)

	// Set status atomically with error handling
	if err := c.SetStateAtomic(false, code.CLIENTDISCONNECTED); err != nil {
		log.Printf("[WARNING] Failed to set disconnect status for client %s: %v", c.id, err)
		// Continue with disconnection even if state update fails
	}

	// Close the connection with a proper close message
	if c.conn != nil {
		err := c.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(
			websocket.CloseNormalClosure, "connection closed"))
		if err != nil {
			log.Printf("[ERROR] Failed to send close message to client %s: %v", c.id, err)
		}
	}

	if c.typeName == "door-lock" {

		clientID64, err := strconv.ParseInt(c.id, 10, 64)
		if err != nil {
			log.Printf("[ERROR] Failed to parse client ID: %v", err)
		}

		err = wsR.query.UpdateDoorOnlineStatus(context.Background(), database.UpdateDoorOnlineStatusParams{
			IsOnline: false,
			ID:       clientID64,
		})
		if err != nil {
			log.Printf("[ERROR] Failed to update door online status: %v", err)
		}

	} else if c.typeName == "qr-in" || c.typeName == "qr-out" {

		qrClient, ok := c.Data.(*QRConnType)
		if !ok {
			log.Printf("[ERROR] Failed to cast to QRConnType")
		}

		doorClient, ok := wsR.hub.GetClient(qrClient.ReqDevice)
		if !ok {
			log.Printf("[ERROR] Failed to get door client")
		}

		doorClientData, ok := doorClient.Data.(*DoorLockType)
		if !ok {
			log.Printf("[ERROR] Failed to cast to DoorLockType")
		}
		doorClientData.RemoveQRDevice(c.id)

	}

	// Close the actual connection safely
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	// Close the send channel safely with panic protection
	if c.send != nil {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("[ERROR] Panic during channel close for client %s: %v", c.id, r)
			}
			c.send = nil
		}()

		// Drain channel to prevent goroutine blocks
		go func() {
			for range c.send {
				// Drain remaining messages
			}
		}()

		close(c.send)
	}

	// Notify the hub about client disconnection if a hub is provided
	if wsR != nil && wsR.hub != nil {
		// Use non-blocking send to avoid deadlocks
		select {
		case wsR.hub.clientLeave <- c:
			log.Printf("[INFO] Successfully notified hub about client %s leaving", c.id)
		case <-time.After(100 * time.Millisecond):
			// Timeout - hub might be busy or closed
			log.Printf("[WARN] Timeout notifying hub about client %s leaving", c.id)
		default:
			// Channel full or closed, log and continue
			log.Printf("[WARN] Could not notify hub about client %s leaving (channel full)", c.id)
		}
	}
}

func (c *Client) WriteToConnText(msg interface{}) error {
	c.Mut.Lock()
	defer c.Mut.Unlock()

	if c.conn == nil {
		return errors.New("connection is closed")
	}

	dataBytes, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	c.conn.SetWriteDeadline(time.Now().Add(c.connMeta.WriteWait))
	err = c.conn.WriteMessage(websocket.TextMessage, dataBytes)
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) WriteToConn(wstype int, msg []byte) error {
	c.conn.SetWriteDeadline(time.Now().Add(c.connMeta.WriteWait))
	if err := c.conn.WriteMessage(wstype, msg); err != nil {
		return err
	}
	return nil
}

func (c *Client) SendMsg(data interface{}) error {
	// Marshal the data to JSON outside the lock
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	// Use select with a timeout to avoid blocking indefinitely
	select {
	case c.send <- jsonData:
		return nil
	case <-time.After(1 * time.Second):
		return errors.New("send timeout: channel might be full")
	default:
		// Check if channel is closed or full without blocking
		c.Mut.RLock()
		isClosed := c.send == nil // A simple check, though not foolproof
		c.Mut.RUnlock()

		if isClosed {
			return errors.New("send channel is closed")
		}

		// If not closed but couldn't send immediately, try with timeout
		select {
		case c.send <- jsonData:
			return nil
		case <-time.After(1 * time.Second):
			return errors.New("send timeout: channel might be full")
		}
	}
}

func (c *Client) WritePipe(W *WSRoutes) {
	ticker := time.NewTicker(c.connMeta.PingTime)
	defer func() {
		// Ensure cleanup happens even if there's a panic
		if r := recover(); r != nil {
			log.Printf("[ERROR] Panic in WritePipe for client %s: %v", c.id, r)
		}
		ticker.Stop()
		c.SafeDisconnect(W)
	}()

	for {
		select {
		case msg, ok := <-c.send:
			if !ok {
				// The hub closed the channel.
				c.Mut.Lock()
				if c.conn != nil {
					c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				}
				c.Mut.Unlock()
				return
			}
			var parsedMsg types.MessageType
			if err := json.Unmarshal(msg, &parsedMsg); err != nil {
				log.Printf("[ERROR] Failed to parse message: %v", err)
				c.conn.WriteMessage(websocket.TextMessage, []byte("message parsing issue"))
				continue
			}
			log.Printf("[WritePipe] >> parsed msg>>: %v", parsedMsg.MsgType)
			log.Printf("[WritePipe] >> parsed msg: %v", parsedMsg.Data)
			writeCtx := &MessageContext{
				Client:  c,
				Ws:      W,
				Message: parsedMsg,
			}
			err := c.Data.EventHandler(writeCtx)
			if err != nil {
				log.Printf("[ERROR] Event handler error: %v", err)
				continue
			}
		case <-ticker.C:
			c.Mut.RLock()
			if c.conn == nil {
				c.Mut.RUnlock()
				log.Printf("[ERROR] Connection is nil for client: %s", c.id)
				return
			}

			c.conn.SetWriteDeadline(time.Now().Add(c.connMeta.WriteWait))
			err := c.conn.WriteMessage(websocket.PingMessage, nil)
			c.Mut.RUnlock()

			if err != nil {
				log.Printf("[ERROR] Ping error for client %s: %v", c.id, err)
				return
			}
		}
	}
}

func (c *Client) ReadPipe(w *WSRoutes) {
	defer func() {
		// Ensure client cleanup happens even if there's a panic
		c.SafeDisconnect(w)
		if r := recover(); r != nil {
			log.Printf("[ERROR] Panic in ReadPipe: %v", r)
		}
	}()

	// Set the read deadline based on the connection metadata
	c.conn.SetReadDeadline(time.Now().Add(c.connMeta.PongTime))

	// Set up pong handler to reset the read deadline each time a pong is received
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(c.connMeta.PongTime))
		return nil
	})

	for {
		// Read message from WebSocket
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err,
				websocket.CloseGoingAway,
				websocket.CloseNormalClosure,
				websocket.CloseNoStatusReceived) {
				log.Printf("[ERROR] Unexpected close error: %v", err)
			} else {
				log.Printf("[INFO] Connection closed: %v", err)
			}
			break // Exit the loop on any read error
		}

		// Reset read deadline after successful read
		c.conn.SetReadDeadline(time.Now().Add(c.connMeta.PongTime))

		// Handle the received message
		var parsedMsg types.MessageType
		if err := json.Unmarshal(message, &parsedMsg); err != nil {
			log.Printf("[ERROR] Failed to parse message: %v", err)

			// Create error response and marshal to JSON
			errorData := map[string]interface{}{
				"message": "Invalid message format",
				"error":   err.Error(),
			}

			errorMsg := types.MessageType{
				MsgType: "error",
			}

			// Convert the map to JSON bytes for the Data field
			dataBytes, _ := json.Marshal(errorData)
			errorMsg.Data = dataBytes

			// Marshal the entire message
			errorBytes, _ := json.Marshal(errorMsg)
			c.send <- errorBytes
			continue
		}

		// Validate message before processing
		if parsedMsg.MsgType == "" {
			log.Printf("[ERROR] Message type is empty")

			// Create error response for empty type
			errorData := map[string]interface{}{
				"message": "Message type is required",
			}

			errorMsg := types.MessageType{
				MsgType: "error",
			}

			// Convert the map to JSON bytes for the Data field
			dataBytes, _ := json.Marshal(errorData)
			errorMsg.Data = dataBytes

			errorBytes, _ := json.Marshal(errorMsg)
			c.send <- errorBytes
			continue
		}

		// Check if client is banned
		c.Mut.RLock()
		isBanned := c.isBanned
		c.Mut.RUnlock()

		if isBanned {
			log.Printf("[INFO] Banned client tried to send message: %s", c.id)

			// Create ban message
			banData := map[string]interface{}{
				"message": "Your connection is banned",
			}

			banMsg := types.MessageType{
				MsgType: "error",
			}

			// Convert the map to JSON bytes for the Data field
			dataBytes, _ := json.Marshal(banData)
			banMsg.Data = dataBytes

			banBytes, _ := json.Marshal(banMsg)
			c.send <- banBytes
			continue
		}

		// Forward message to write pipe for processing
		c.send <- message
	}
}
