package ws

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"runtime"
	"sync"
	"time"
)

type Hub struct {
	clients     map[string]*Client
	clientJoin  chan *Client
	clientLeave chan *Client
	mut         sync.RWMutex
}

func NewHub() *Hub {
	return &Hub{
		clients:     make(map[string]*Client),
		clientJoin:  make(chan *Client),
		clientLeave: make(chan *Client),
	}
}

func (h *Hub) Start(ctx context.Context) {
	log.Print("--------- Hub Started ---------")
	for {
		select {
		case client := <-h.clientJoin:
			h.mut.Lock()
			h.clients[client.id] = client
			log.Print("[HUB] >> Client Added")
			h.mut.Unlock()

		case client := <-h.clientLeave:
			h.mut.Lock()
			if _, exists := h.clients[client.id]; exists {
				client.Close()
				delete(h.clients, client.id)
				log.Print("[HUB] >> Client Delete")
			}
			h.mut.Unlock()
		case <-ctx.Done():
			log.Print("[HUB] >> Hub Stopped")
			h.Stop()
			return
		}
	}
}

func (h *Hub) GetClient(id string) (*Client, bool) {
	h.mut.RLock()
	defer h.mut.RUnlock()
	client, exists := h.clients[id]
	return client, exists
}

func (h *Hub) getClientsByBranch(id int) []*Client {
	h.mut.RLock()
	var clients []*Client
	for _, client := range h.clients {
		if client.branchID == id {
			clients = append(clients, client)
		}
	}
	h.mut.RUnlock()
	return clients
}

func (h *Hub) Stop() {
	h.mut.Lock()
	defer h.mut.Unlock()

	// Close all client connections
	for _, client := range h.clients {
		client.Close()
	}

	// Clear the map
	h.clients = make(map[string]*Client)
}

// SendToClient sends a message to a specific client by ID
// func (h *Hub) SendToClient(clientID string, message interface{}) error {
// 	client, exists := h.GetClient(clientID)
// 	if !exists {
// 		return errors.New("client not found")
// 	}

// 	// Convert message to JSON
// 	jsonData, err := json.Marshal(message)
// 	if err != nil {
// 		return err
// 	}

// 	select {
// 	case client.send <- jsonData:
// 		log.Printf("[HUB] >> Message sent to client: %s", clientID)
// 		return nil
// 	default:
// 		// If the client's send buffer is full
// 		return errors.New("client message buffer full")
// 	}
// }

func (h *Hub) SendToClient(clientID string, message interface{}) error {
	log.Printf("[HUB1] >> Sending message to client: %s", clientID)
	h.mut.RLock()
	client, exists := h.clients[clientID]
	h.mut.RUnlock()

	if !exists {
		return errors.New("client not found")
	}

	// Check if client is banned
	client.Mut.RLock()
	isBanned := client.isBanned
	client.Mut.RUnlock()

	if isBanned {
		return errors.New("client is banned")
	}

	// Check connection health before sending
	client.Mut.RLock()
	conn := client.conn
	client.Mut.RUnlock()

	if conn == nil {
		return errors.New("client connection is closed")
	}

	// Convert message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	// Use a timeout to avoid blocking indefinitely
	select {
	case client.send <- jsonData:
		log.Printf("[HUB] >> Message sent to client: %s", clientID)
		return nil
	case <-time.After(500 * time.Millisecond):
		return errors.New("client message buffer full or send timeout")
	}
}

// BroadcastToBranch sends a message to all clients in a specific branch
func (h *Hub) BroadcastToBranch(branchID int, message interface{}) error {
	clients := h.getClientsByBranch(branchID)
	if len(clients) == 0 {
		return errors.New("no clients found in the specified branch")
	}

	// Convert message to JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal broadcast message: %w", err)
	}

	// Track success and failure counts
	successCount := 0
	failureCount := 0
	var failedClients []string

	// Send to all clients in the branch
	for _, client := range clients {
		// Check connection health before sending
		client.Mut.RLock()
		conn := client.conn
		clientID := client.id
		client.Mut.RUnlock()

		if conn == nil {
			failureCount++
			failedClients = append(failedClients, clientID)
			log.Printf("[HUB] >> Failed to send broadcast to client %s (connection closed)", clientID)
			continue
		}

		select {
		case client.send <- jsonData:
			successCount++
		default:
			failureCount++
			failedClients = append(failedClients, clientID)
			log.Printf("[HUB] >> Failed to send broadcast to client %s (buffer full)", clientID)
		}
	}

	log.Printf("[HUB] >> Broadcast to branch %d completed: %d success, %d failed", branchID, successCount, failureCount)

	if failureCount > 0 {
		return fmt.Errorf("broadcast partially failed: %d/%d clients failed (%v)", failureCount, len(clients), failedClients)
	}

	return nil
}

//	func (h *Hub) GetClients() map[string]*Client {
//		h.mut.RLock()
//		defer h.mut.RUnlock()
//		return h.clients
//	}
func (h *Hub) GetClientsData() map[string]interface{} {
	h.mut.RLock()
	defer h.mut.RUnlock()

	// Create a new map to hold the transformed data
	clientsData := make(map[string]interface{})

	for id, client := range h.clients {
		client.Mut.RLock() // Lock the client mutex to safely access its fields

		// Transform the client data into a user-friendly format
		clientsData[id] = map[string]interface{}{
			"id":         client.id,
			"branchID":   client.branchID,
			"typeName":   client.typeName,
			"processing": client.GetProcessing(),
			"status":     client.GetStatus(),
			"data":       client.Data, // Assuming `Data` implements a user-friendly JSON representation
		}

		client.Mut.RUnlock()
	}

	return clientsData
}

func (h *Hub) GetDoorClients() map[string]*Client {
	h.mut.RLock()
	defer h.mut.RUnlock()

	doorClients := make(map[string]*Client)
	for id, client := range h.clients {
		if client.typeName == "door-lock" {
			doorClients[id] = client
		}
	}
	return doorClients
}

func (h *Hub) RemoveOnlineClient(clientID string, wrr *WSRoutes) {
	h.mut.Lock()
	defer h.mut.Unlock()

	if client, exists := h.clients[clientID]; exists {
		client.SafeDisconnect(wrr)
		delete(h.clients, clientID)
		log.Printf("[HUB] >> Client %s removed", clientID)
	} else {
		log.Printf("[HUB] >> Client %s not found", clientID)
	}
}

func (h *Hub) CheckClientExistence(clientID string) bool {
	h.mut.RLock()
	defer h.mut.RUnlock()

	_, exists := h.clients[clientID]
	return exists
}

// ClearAllClients safely closes and removes all existing client connections
func (h *Hub) ClearAllClients() {
	h.mut.Lock()
	defer h.mut.Unlock()

	clientCount := len(h.clients)
	if clientCount == 0 {
		log.Printf("[HUB] >> No clients to clear")
		return
	}

	// Create a slice to hold client references for cleanup
	clientsToClose := make([]*Client, 0, clientCount)
	clientIDs := make([]string, 0, clientCount)

	// Collect all clients first to avoid modifying map during iteration
	for id, client := range h.clients {
		clientsToClose = append(clientsToClose, client)
		clientIDs = append(clientIDs, id)
	}

	// Clear the map immediately to prevent new operations
	h.clients = make(map[string]*Client)

	// Close all client connections with proper error handling
	successCount := 0
	failureCount := 0

	for i, client := range clientsToClose {
		clientID := clientIDs[i]
		log.Printf("[HUB] >> Closing client: %s during hub cleanup", clientID)

		// Use defer to ensure we don't panic on individual client cleanup
		func() {
			defer func() {
				if r := recover(); r != nil {
					log.Printf("[ERROR] Panic during cleanup of client %s: %v", clientID, r)
					failureCount++
				}
			}()

			client.Close()
			successCount++
		}()
	}

	// Force garbage collection hint for large cleanups
	if clientCount > 50 {
		runtime.GC()
	}

	log.Printf("[HUB] >> All clients cleared (%d total, %d success, %d failures)",
		clientCount, successCount, failureCount)
}
