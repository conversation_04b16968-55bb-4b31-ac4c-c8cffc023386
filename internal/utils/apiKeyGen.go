package utils

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"crypto/subtle"
	"database/sql"
	"encoding/base64"
	"encoding/hex"
	"fmt"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
)

// GenerateAPI<PERSON>ey creates a new secure API key for a door lock device
// Returns the plaintext API key for client use and a hash for storage
func GenerateAPIKey(db *database.Queries, doorLockID int64) (string, error) {
	// Generate a secure random API key (32 bytes = 256 bits of entropy)
	keyBytes := make([]byte, 32)
	_, err := rand.Read(keyBytes)
	if err != nil {
		return "", fmt.Errorf("failed to generate secure API key: %w", err)
	}

	// Encode the random bytes as base64 to make it usable as an API key
	// This is what will be provided to the client
	apiKey := base64.URLEncoding.EncodeToString(keyBytes)

	// For storage, we'll hash the API key
	// This is optional but recommended for additional security
	hashedKey := HashAPIKey(apiKey)

	// Store the hashed key in the database
	err = storeAPIKey(db, doorLockID, hashedKey)
	if err != nil {
		return "", fmt.Errorf("failed to store API key: %w", err)
	}

	return apiKey, nil
}

// hashAPIKey creates a SHA-256 hash of the API key for secure storage
func HashAPIKey(apiKey string) string {
	hash := sha256.Sum256([]byte(apiKey))
	return hex.EncodeToString(hash[:])
}

// storeAPIKey updates the door lock record with the new hashed API key
func storeAPIKey(db *database.Queries, doorLockID int64, hashedKey string) error {
	// Using the GenApiKeyForDoorLock query from your SQL
	_, err := db.GenApiKeyForDoorLock(context.Background(), database.GenApiKeyForDoorLockParams{
		ID:     doorLockID,
		ApiKey: hashedKey,
	})

	return err
}

// ValidateAPIKey checks if a provided API key matches the stored hash
// This would be used when a device tries to authenticate
func ValidateAPIKey(db *database.Queries, doorLockID int64, providedKey string) (bool, error) {
	// Get the stored hashed key from the database
	storedKey, err := db.GetApiKey(context.Background(), doorLockID)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil // Device not found
		}
		return false, fmt.Errorf("database error: %w", err)
	}

	// Hash the provided key and compare
	hashedProvidedKey := HashAPIKey(providedKey)

	// Constant-time comparison to prevent timing attacks
	return subtle.ConstantTimeCompare([]byte(hashedProvidedKey), []byte(storedKey)) == 1, nil
}
