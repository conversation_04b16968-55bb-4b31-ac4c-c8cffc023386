package utils

import (
	"strings"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// TestJWTValidationErrorHandling tests various JWT validation error scenarios
func TestJWTValidationErrorHandling(t *testing.T) {
	
	t.Run("ValidateToken_MalformedToken", func(t *testing.T) {
		malformedTokens := []string{
			"",                           // Empty token
			"invalid.token",              // Invalid format
			"not.a.jwt.token",           // Too many parts
			"invalid",                   // Single part
			"header.payload",            // Missing signature
			"header.payload.signature.extra", // Too many parts
		}
		
		for _, token := range malformedTokens {
			t.Run("Token_"+token, func(t *testing.T) {
				_, err := ValidateToken(token)
				if err == nil {
					t.Errorf("Expected error for malformed token '%s', got nil", token)
				}
				t.Logf("✅ Correctly rejected malformed token: %v", err)
			})
		}
	})

	t.Run("ValidateToken_ExpiredToken", func(t *testing.T) {
		// Create an expired token
		info := QRCodeInfo{
			ReqDeviceId: "test-device",
			BranchId:    1,
			ClientId:    "test-client",
			ConnType:    "qr-in",
		}
		
		// Generate token that expires immediately
		token, err := GenerateJWTToken(info, -time.Hour) // Expired 1 hour ago
		if err != nil {
			t.Fatalf("Failed to generate expired token: %v", err)
		}
		
		_, err = ValidateToken(token)
		if err == nil {
			t.Error("Expected error for expired token, got nil")
		}
		
		if !strings.Contains(err.Error(), "expired") {
			t.Errorf("Expected 'expired' in error message, got: %v", err)
		}
		t.Logf("✅ Correctly rejected expired token: %v", err)
	})

	t.Run("ValidateToken_InvalidSignature", func(t *testing.T) {
		// Create a valid token
		info := QRCodeInfo{
			ReqDeviceId: "test-device",
			BranchId:    1,
			ClientId:    "test-client",
			ConnType:    "qr-in",
		}
		
		token, err := GenerateJWTToken(info, time.Hour)
		if err != nil {
			t.Fatalf("Failed to generate token: %v", err)
		}
		
		// Tamper with the signature
		parts := strings.Split(token, ".")
		if len(parts) != 3 {
			t.Fatalf("Invalid token format")
		}
		
		// Change the last character of the signature
		tamperedSignature := parts[2][:len(parts[2])-1] + "X"
		tamperedToken := parts[0] + "." + parts[1] + "." + tamperedSignature
		
		_, err = ValidateToken(tamperedToken)
		if err == nil {
			t.Error("Expected error for tampered token, got nil")
		}
		t.Logf("✅ Correctly rejected tampered token: %v", err)
	})

	t.Run("ValidateToken_WrongSigningMethod", func(t *testing.T) {
		// Create a token with a different signing method
		info := QRCodeInfo{
			ReqDeviceId: "test-device",
			BranchId:    1,
			ClientId:    "test-client",
			ConnType:    "qr-in",
		}
		
		now := time.Now()
		claims := Claims{
			QRInfo: info,
			RegisteredClaims: jwt.RegisteredClaims{
				Issuer:    "your-app-name",
				Audience:  []string{"your-api"},
				IssuedAt:  jwt.NewNumericDate(now),
				NotBefore: jwt.NewNumericDate(now),
				ExpiresAt: jwt.NewNumericDate(now.Add(time.Hour)),
				ID:        info.ClientId + "-" + now.Format(time.RFC3339Nano),
			},
		}
		
		// Use RS256 instead of HS256 (this will fail because we don't have the right key)
		token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
		tokenString, _ := token.SignedString([]byte("fake-key")) // This will create an invalid token
		
		_, err := ValidateToken(tokenString)
		if err == nil {
			t.Error("Expected error for wrong signing method, got nil")
		}
		t.Logf("✅ Correctly rejected token with wrong signing method: %v", err)
	})

	t.Run("ValidateToken_ClockSkewIssues", func(t *testing.T) {
		// Test tokens with future timestamps (clock skew)
		info := QRCodeInfo{
			ReqDeviceId: "test-device",
			BranchId:    1,
			ClientId:    "test-client",
			ConnType:    "qr-in",
		}
		
		// Create claims with future issued time (simulating clock skew)
		futureTime := time.Now().Add(time.Hour)
		claims := Claims{
			QRInfo: info,
			RegisteredClaims: jwt.RegisteredClaims{
				Issuer:    "your-app-name",
				Audience:  []string{"your-api"},
				IssuedAt:  jwt.NewNumericDate(futureTime), // Future time
				NotBefore: jwt.NewNumericDate(futureTime), // Future time
				ExpiresAt: jwt.NewNumericDate(futureTime.Add(time.Hour)),
				ID:        info.ClientId + "-" + futureTime.Format(time.RFC3339Nano),
			},
		}
		
		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := token.SignedString(jwtKey)
		if err != nil {
			t.Fatalf("Failed to sign token: %v", err)
		}
		
		_, err = ValidateToken(tokenString)
		// Current implementation might not handle clock skew properly
		t.Logf("Clock skew test result: %v", err)
		t.Log("❌ ISSUE: Current implementation may not handle clock skew edge cases")
	})

	t.Run("ValidateToken_EmptyOrNilClaims", func(t *testing.T) {
		// Test with minimal/empty claims
		emptyClaims := Claims{}
		
		token := jwt.NewWithClaims(jwt.SigningMethodHS256, emptyClaims)
		tokenString, err := token.SignedString(jwtKey)
		if err != nil {
			t.Fatalf("Failed to sign token: %v", err)
		}
		
		claims, err := ValidateToken(tokenString)
		if err != nil {
			t.Logf("Empty claims validation error: %v", err)
		} else {
			t.Logf("Empty claims validation succeeded: %+v", claims)
		}
		
		// Check if the claims are properly initialized
		if claims != nil && claims.QRInfo.ReqDeviceId == "" {
			t.Log("❌ ISSUE: Empty QRInfo fields not properly validated")
		}
	})

	t.Run("ValidateToken_ExtremelyLongToken", func(t *testing.T) {
		// Test with extremely long token (potential DoS)
		longString := strings.Repeat("a", 10000)
		
		_, err := ValidateToken(longString)
		if err == nil {
			t.Error("Expected error for extremely long token, got nil")
		}
		t.Logf("✅ Correctly rejected extremely long token: %v", err)
	})

	t.Run("ValidateToken_SpecialCharacters", func(t *testing.T) {
		// Test with tokens containing special characters
		specialTokens := []string{
			"header.payload.signature\x00", // Null byte
			"header.payload.signature\n",   // Newline
			"header.payload.signature\r",   // Carriage return
			"header.payload.signature\t",   // Tab
		}
		
		for _, token := range specialTokens {
			_, err := ValidateToken(token)
			if err == nil {
				t.Errorf("Expected error for token with special characters, got nil")
			}
			t.Logf("✅ Correctly rejected token with special characters: %v", err)
		}
	})
}

// TestJWTGenerationErrorHandling tests error scenarios in token generation
func TestJWTGenerationErrorHandling(t *testing.T) {
	
	t.Run("GenerateJWTToken_ZeroDuration", func(t *testing.T) {
		info := QRCodeInfo{
			ReqDeviceId: "test-device",
			BranchId:    1,
			ClientId:    "test-client",
			ConnType:    "qr-in",
		}
		
		token, err := GenerateJWTToken(info, 0)
		if err != nil {
			t.Errorf("Unexpected error for zero duration: %v", err)
		}
		
		// Token should be immediately expired
		_, err = ValidateToken(token)
		if err == nil {
			t.Error("Expected error for immediately expired token, got nil")
		}
		t.Logf("✅ Zero duration token correctly expires immediately: %v", err)
	})

	t.Run("GenerateJWTToken_NegativeDuration", func(t *testing.T) {
		info := QRCodeInfo{
			ReqDeviceId: "test-device",
			BranchId:    1,
			ClientId:    "test-client",
			ConnType:    "qr-in",
		}
		
		token, err := GenerateJWTToken(info, -time.Hour)
		if err != nil {
			t.Errorf("Unexpected error for negative duration: %v", err)
		}
		
		// Token should be expired
		_, err = ValidateToken(token)
		if err == nil {
			t.Error("Expected error for pre-expired token, got nil")
		}
		t.Logf("✅ Negative duration token correctly expires: %v", err)
	})

	t.Run("GenerateTokenForToday_EdgeCases", func(t *testing.T) {
		info := QRCodeInfo{
			ReqDeviceId: "test-device",
			BranchId:    1,
			ClientId:    "test-client",
			ConnType:    "qr-in",
		}
		
		// Test near midnight
		token, err := GenerateTokenForToday(info)
		if err != nil {
			t.Errorf("Unexpected error generating token for today: %v", err)
		}
		
		// Validate the token
		claims, err := ValidateToken(token)
		if err != nil {
			t.Errorf("Generated token is invalid: %v", err)
		}
		
		if claims != nil {
			t.Logf("✅ Token for today generated successfully, expires at: %v", claims.ExpiresAt.Time)
		}
	})
}
