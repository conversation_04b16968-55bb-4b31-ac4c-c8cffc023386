package utils

import (
	"crypto/rand"
	"encoding/base64"
	"log"
	"sync"
	"time"

	"github.com/labstack/echo/v4"
)

type TokenStore struct {
	tokenStore   map[string]TokenInfo
	mut          sync.Mutex
	clear        chan bool
	isOnlineFunc func(deviceId string) bool
}

type TokenInfo struct {
	ClientId string
	ConnType string
	BranchId int
	ApiKey   string
	ReqId    string
	ExpDate  time.Time
}

// generateSecureToken creates a cryptographically secure random token
func GenerateSecureToken() string {
	// Create a byte slice to hold random bytes
	b := make([]byte, 32) // 32 bytes = 256 bits
	// Read random data into the byte slice
	_, err := rand.Read(b)
	if err != nil {
		// In production code, handle this error appropriately
		log.Printf("Error generating random token: %v", err)
		return ""
	}
	// Encode the random bytes as a base64 string for easy transmission
	// Use URL-safe encoding to avoid issues with special characters
	return base64.URLEncoding.EncodeToString(b)
}

// NewTokenStore creates and initializes a token store with automatic cleanup
func NewTokenStore(cleanupInterval time.Duration) *TokenStore {
	store := &TokenStore{
		tokenStore: make(map[string]TokenInfo),
		clear:      make(chan bool),
	}
	log.Print("--------- Starting token store ---------")
	// Start the cleanup goroutine
	go store.startCleanupRoutine(cleanupInterval)
	return store
}

func (ts *TokenStore) SetIsOnlineFunc(fn func(deviceId string) bool) {
	ts.mut.Lock()
	defer ts.mut.Unlock()
	ts.isOnlineFunc = fn
}

func (ts *TokenStore) StoreToken(token string, clientId string, connType string, branchId int, reqId string, apiKey string, expAt time.Time) {
	ts.mut.Lock()
	defer ts.mut.Unlock()
	ts.tokenStore[token] = TokenInfo{
		ClientId: clientId,
		ConnType: connType,
		BranchId: branchId,
		ReqId:    reqId,
		ApiKey:   apiKey,
		ExpDate:  expAt,
	}
	log.Printf(">> add token : %v", ts.tokenStore[token])
}

func (ts *TokenStore) ValidateToken(token string) (tokenInfo TokenInfo, valid bool) {
	ts.mut.Lock()
	defer ts.mut.Unlock()
	info, exists := ts.tokenStore[token]
	if !exists {
		return TokenInfo{}, false
	}
	if time.Now().After(info.ExpDate) {
		return TokenInfo{}, false
	}
	return info, true
}

// RemoveToken explicitly removes a token
func (ts *TokenStore) RemoveToken(token string) {
	ts.mut.Lock()
	defer ts.mut.Unlock()
	delete(ts.tokenStore, token)
}

// startCleanupRoutine periodically removes expired tokens
func (ts *TokenStore) startCleanupRoutine(interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()
	log.Print(">> Starting token store cleanup route")
	for {
		select {
		case <-ticker.C:
			ts.cleanupExpiredTokens()
		case <-ts.clear:
			return
		}
	}
}

// cleanupExpiredTokens removes all expired tokens from the store
func (ts *TokenStore) cleanupExpiredTokens() {
	now := time.Now()
	ts.mut.Lock()
	defer ts.mut.Unlock()
	for token, info := range ts.tokenStore {
		if now.After(info.ExpDate) {
			delete(ts.tokenStore, token)
		}
	}
}

// Shutdown stops the cleanup routine
func (ts *TokenStore) Shutdown() {
	close(ts.clear)
}

func (ts *TokenStore) DrawTUITokenStoreData() {
	// This function is a placeholder for drawing the token store data in a TUI
	// Implement your TUI drawing logic here
	log.Print("Drawing token store data...")
	for token, info := range ts.tokenStore {
		log.Printf("Token: %s, Info: %+v", token, info)
	}
}

func (ts *TokenStore) GetAllTokens(c echo.Context) error {
	ts.mut.Lock()
	defer ts.mut.Unlock()

	// Create a slice to hold all tokens
	tokens := make([]TokenInfo, 0, len(ts.tokenStore))

	// Iterate over the token store and append each token to the slice
	for _, info := range ts.tokenStore {
		tokens = append(tokens, info)
	}

	// Return the tokens as JSON
	return c.JSON(200, tokens)
}

// DeleteTokensByClientID allows deleting all tokens for a specific client
func (ts *TokenStore) DeleteTokensByClientID(c echo.Context) error {
	clientID := c.Param("client")
	if clientID == "" {
		return c.JSON(400, map[string]string{"error": "Missing clientID parameter"})
	}

	ts.mut.Lock()
	defer ts.mut.Unlock()

	// Find and delete all tokens belonging to the client
	deletedCount := 0
	for token, info := range ts.tokenStore {
		if info.ClientId == clientID {
			delete(ts.tokenStore, token)
			deletedCount++
		}
	}

	if deletedCount == 0 {
		return c.JSON(404, map[string]string{"error": "No tokens found for the specified client"})
	}

	log.Printf("Deleted %d tokens for client: %s", deletedCount, clientID)
	return c.JSON(200, map[string]interface{}{
		"message":      "Tokens deleted successfully",
		"deletedCount": deletedCount,
	})
}

func (ts *TokenStore) ResetTokenStore(c echo.Context) error {
	// Authentication/authorization should be implemented here
	// This is a sensitive operation that should be restricted

	// Optional: Add an API key check or other auth mechanis

	ts.mut.Lock()
	defer ts.mut.Unlock()

	// Save token count before reset for logging
	tokenCount := len(ts.tokenStore)

	// Create new maps instead of clearing existing ones
	// This is safer as it doesn't affect any ongoing operations on the old maps
	ts.tokenStore = make(map[string]TokenInfo)

	log.Printf("Token store reset: %d tokens removed", tokenCount)

	return c.JSON(200, map[string]interface{}{
		"message":       "Token store reset successfully",
		"tokensRemoved": tokenCount,
	})
}
