// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: door_lock.sql

package database

import (
	"context"
	"database/sql"
)

const banDoorLock = `-- name: BanDoorLock :exec
UPDATE reg_door_lock 
SET is_ban = TRUE, updated_at = CURRENT_TIMESTAMP 
WHERE id = ?
`

func (q *Queries) BanDoorLock(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, banDoorLock, id)
	return err
}

const deleteDoorLock = `-- name: DeleteDoorLock :exec
DELETE FROM reg_door_lock 
WHERE id = ?
`

func (q *Queries) DeleteDoorLock(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteDoorLock, id)
	return err
}

const genApiKeyForDoorLock = `-- name: GenApiKeyForDoorLock :execresult
UPDATE reg_door_lock 
SET api_key = ?, updated_at = CURRENT_TIMESTAMP
WHERE id = ?
`

type GenApiKeyForDoorLockParams struct {
	ApiKey string `json:"api_key"`
	ID     int64  `json:"id"`
}

func (q *Queries) GenApiKeyForDoorLock(ctx context.Context, arg GenApiKeyForDoorLockParams) (sql.Result, error) {
	return q.db.ExecContext(ctx, genApiKeyForDoorLock, arg.ApiKey, arg.ID)
}

const generateSessionKey = `-- name: GenerateSessionKey :execresult
UPDATE reg_door_lock 
SET session_key = ?, last_session_key = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP 
WHERE id = ?
`

type GenerateSessionKeyParams struct {
	SessionKey sql.NullString `json:"session_key"`
	ID         int64          `json:"id"`
}

func (q *Queries) GenerateSessionKey(ctx context.Context, arg GenerateSessionKeyParams) (sql.Result, error) {
	return q.db.ExecContext(ctx, generateSessionKey, arg.SessionKey, arg.ID)
}

const getAllDevices = `-- name: GetAllDevices :many
SELECT d.id, d.device_name, d.location, d.branch_id, b.name AS branch_name, 
       d.meta, d.is_online, d.is_ban, d.last_online, d.created_at, d.updated_at
FROM reg_door_lock d
JOIN HR_Branch b ON d.branch_id = b.id
ORDER BY d.branch_id, d.id
`

type GetAllDevicesRow struct {
	ID         int64          `json:"id"`
	DeviceName string         `json:"device_name"`
	Location   string         `json:"location"`
	BranchID   int64          `json:"branch_id"`
	BranchName string         `json:"branch_name"`
	Meta       sql.NullString `json:"meta"`
	IsOnline   bool           `json:"is_online"`
	IsBan      bool           `json:"is_ban"`
	LastOnline sql.NullTime   `json:"last_online"`
	CreatedAt  sql.NullTime   `json:"created_at"`
	UpdatedAt  sql.NullTime   `json:"updated_at"`
}

func (q *Queries) GetAllDevices(ctx context.Context) ([]GetAllDevicesRow, error) {
	rows, err := q.db.QueryContext(ctx, getAllDevices)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllDevicesRow
	for rows.Next() {
		var i GetAllDevicesRow
		if err := rows.Scan(
			&i.ID,
			&i.DeviceName,
			&i.Location,
			&i.BranchID,
			&i.BranchName,
			&i.Meta,
			&i.IsOnline,
			&i.IsBan,
			&i.LastOnline,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getApiKey = `-- name: GetApiKey :one
SELECT api_key 
FROM reg_door_lock 
WHERE id = ?
`

func (q *Queries) GetApiKey(ctx context.Context, id int64) (string, error) {
	row := q.db.QueryRowContext(ctx, getApiKey, id)
	var api_key string
	err := row.Scan(&api_key)
	return api_key, err
}

const getBranchCount = `-- name: GetBranchCount :one
SELECT COUNT(*) AS total_count FROM HR_Branch
`

func (q *Queries) GetBranchCount(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, getBranchCount)
	var total_count int64
	err := row.Scan(&total_count)
	return total_count, err
}

const getDeviceCountByBranch = `-- name: GetDeviceCountByBranch :many
SELECT b.id AS branch_id, b.name AS branch_name, COUNT(d.id) AS device_count
FROM HR_Branch b
LEFT JOIN reg_door_lock d ON b.id = d.branch_id
GROUP BY b.id, b.name
ORDER BY b.name
`

type GetDeviceCountByBranchRow struct {
	BranchID    int64  `json:"branch_id"`
	BranchName  string `json:"branch_name"`
	DeviceCount int64  `json:"device_count"`
}

func (q *Queries) GetDeviceCountByBranch(ctx context.Context) ([]GetDeviceCountByBranchRow, error) {
	rows, err := q.db.QueryContext(ctx, getDeviceCountByBranch)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetDeviceCountByBranchRow
	for rows.Next() {
		var i GetDeviceCountByBranchRow
		if err := rows.Scan(&i.BranchID, &i.BranchName, &i.DeviceCount); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDeviceData = `-- name: GetDeviceData :one
SELECT d.id, d.device_name, d.location, d.branch_id, b.name AS branch_name, 
       d.meta, d.is_online, d.is_ban, d.last_online, d.created_at, d.updated_at
FROM reg_door_lock d
JOIN HR_Branch b ON d.branch_id = b.id
WHERE d.id = ?
`

type GetDeviceDataRow struct {
	ID         int64          `json:"id"`
	DeviceName string         `json:"device_name"`
	Location   string         `json:"location"`
	BranchID   int64          `json:"branch_id"`
	BranchName string         `json:"branch_name"`
	Meta       sql.NullString `json:"meta"`
	IsOnline   bool           `json:"is_online"`
	IsBan      bool           `json:"is_ban"`
	LastOnline sql.NullTime   `json:"last_online"`
	CreatedAt  sql.NullTime   `json:"created_at"`
	UpdatedAt  sql.NullTime   `json:"updated_at"`
}

func (q *Queries) GetDeviceData(ctx context.Context, id int64) (GetDeviceDataRow, error) {
	row := q.db.QueryRowContext(ctx, getDeviceData, id)
	var i GetDeviceDataRow
	err := row.Scan(
		&i.ID,
		&i.DeviceName,
		&i.Location,
		&i.BranchID,
		&i.BranchName,
		&i.Meta,
		&i.IsOnline,
		&i.IsBan,
		&i.LastOnline,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getDevicesByBranch = `-- name: GetDevicesByBranch :many
SELECT d.id, d.device_name, d.location, d.branch_id, b.name AS branch_name, 
       d.meta, d.is_online, d.is_ban, d.last_online, d.created_at, d.updated_at
FROM reg_door_lock d
JOIN HR_Branch b ON d.branch_id = b.id
WHERE d.branch_id = ?
ORDER BY d.id
`

type GetDevicesByBranchRow struct {
	ID         int64          `json:"id"`
	DeviceName string         `json:"device_name"`
	Location   string         `json:"location"`
	BranchID   int64          `json:"branch_id"`
	BranchName string         `json:"branch_name"`
	Meta       sql.NullString `json:"meta"`
	IsOnline   bool           `json:"is_online"`
	IsBan      bool           `json:"is_ban"`
	LastOnline sql.NullTime   `json:"last_online"`
	CreatedAt  sql.NullTime   `json:"created_at"`
	UpdatedAt  sql.NullTime   `json:"updated_at"`
}

func (q *Queries) GetDevicesByBranch(ctx context.Context, branchID int64) ([]GetDevicesByBranchRow, error) {
	rows, err := q.db.QueryContext(ctx, getDevicesByBranch, branchID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetDevicesByBranchRow
	for rows.Next() {
		var i GetDevicesByBranchRow
		if err := rows.Scan(
			&i.ID,
			&i.DeviceName,
			&i.Location,
			&i.BranchID,
			&i.BranchName,
			&i.Meta,
			&i.IsOnline,
			&i.IsBan,
			&i.LastOnline,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDevicesCount = `-- name: GetDevicesCount :one
SELECT COUNT(*) AS total_count FROM reg_door_lock
`

func (q *Queries) GetDevicesCount(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, getDevicesCount)
	var total_count int64
	err := row.Scan(&total_count)
	return total_count, err
}

const getOnlineDeviceCount = `-- name: GetOnlineDeviceCount :one
SELECT COUNT(*) AS online_devices
FROM reg_door_lock
WHERE is_online = TRUE AND is_ban = FALSE
`

func (q *Queries) GetOnlineDeviceCount(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, getOnlineDeviceCount)
	var online_devices int64
	err := row.Scan(&online_devices)
	return online_devices, err
}

const getSessionKey = `-- name: GetSessionKey :one
SELECT session_key 
FROM reg_door_lock 
WHERE id = ?
`

func (q *Queries) GetSessionKey(ctx context.Context, id int64) (sql.NullString, error) {
	row := q.db.QueryRowContext(ctx, getSessionKey, id)
	var session_key sql.NullString
	err := row.Scan(&session_key)
	return session_key, err
}

const getTotalDeviceCount = `-- name: GetTotalDeviceCount :one
SELECT COUNT(*) AS total_devices
FROM reg_door_lock
`

func (q *Queries) GetTotalDeviceCount(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, getTotalDeviceCount)
	var total_devices int64
	err := row.Scan(&total_devices)
	return total_devices, err
}

const isOnlineDoorLock = `-- name: IsOnlineDoorLock :one
SELECT is_online 
FROM reg_door_lock 
WHERE id = ?
`

func (q *Queries) IsOnlineDoorLock(ctx context.Context, id int64) (bool, error) {
	row := q.db.QueryRowContext(ctx, isOnlineDoorLock, id)
	var is_online bool
	err := row.Scan(&is_online)
	return is_online, err
}

const registerDoorLock = `-- name: RegisterDoorLock :execresult
INSERT INTO reg_door_lock (
     branch_id, device_name, location, api_key, meta
) VALUES (?, ?, ?, ?, ?)
`

type RegisterDoorLockParams struct {
	BranchID   int64          `json:"branch_id"`
	DeviceName string         `json:"device_name"`
	Location   string         `json:"location"`
	ApiKey     string         `json:"api_key"`
	Meta       sql.NullString `json:"meta"`
}

func (q *Queries) RegisterDoorLock(ctx context.Context, arg RegisterDoorLockParams) (sql.Result, error) {
	return q.db.ExecContext(ctx, registerDoorLock,
		arg.BranchID,
		arg.DeviceName,
		arg.Location,
		arg.ApiKey,
		arg.Meta,
	)
}

const setOnlineDoorLock = `-- name: SetOnlineDoorLock :execresult
UPDATE reg_door_lock 
SET is_online = ?, last_online = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP 
WHERE id = ?
`

type SetOnlineDoorLockParams struct {
	IsOnline bool  `json:"is_online"`
	ID       int64 `json:"id"`
}

func (q *Queries) SetOnlineDoorLock(ctx context.Context, arg SetOnlineDoorLockParams) (sql.Result, error) {
	return q.db.ExecContext(ctx, setOnlineDoorLock, arg.IsOnline, arg.ID)
}

const updateDoorLock = `-- name: UpdateDoorLock :execresult
UPDATE reg_door_lock 
SET device_name = ?, location = ?, meta = ?, updated_at = CURRENT_TIMESTAMP 
WHERE id = ?
`

type UpdateDoorLockParams struct {
	DeviceName string         `json:"device_name"`
	Location   string         `json:"location"`
	Meta       sql.NullString `json:"meta"`
	ID         int64          `json:"id"`
}

func (q *Queries) UpdateDoorLock(ctx context.Context, arg UpdateDoorLockParams) (sql.Result, error) {
	return q.db.ExecContext(ctx, updateDoorLock,
		arg.DeviceName,
		arg.Location,
		arg.Meta,
		arg.ID,
	)
}

const updateDoorOnlineStatus = `-- name: UpdateDoorOnlineStatus :exec
UPDATE reg_door_lock
SET 
  is_online = ?, 
  last_online = CURRENT_TIMESTAMP
WHERE id = ?
`

type UpdateDoorOnlineStatusParams struct {
	IsOnline bool  `json:"is_online"`
	ID       int64 `json:"id"`
}

func (q *Queries) UpdateDoorOnlineStatus(ctx context.Context, arg UpdateDoorOnlineStatusParams) error {
	_, err := q.db.ExecContext(ctx, updateDoorOnlineStatus, arg.IsOnline, arg.ID)
	return err
}

const validateDeviceDetails = `-- name: ValidateDeviceDetails :many
SELECT 
    dl.id,
    dl.device_name,
    dl.location,
    dl.is_online,
    dl.is_ban,
    b.name as branch_name
FROM 
    reg_door_lock dl
JOIN 
    HR_Branch b ON dl.branch_id = b.id
WHERE 
    dl.api_key = ?
    AND dl.branch_id = ?
    AND dl.id = ?
    AND dl.is_ban = FALSE
`

type ValidateDeviceDetailsParams struct {
	ApiKey   string `json:"api_key"`
	BranchID int64  `json:"branch_id"`
	ID       int64  `json:"id"`
}

type ValidateDeviceDetailsRow struct {
	ID         int64  `json:"id"`
	DeviceName string `json:"device_name"`
	Location   string `json:"location"`
	IsOnline   bool   `json:"is_online"`
	IsBan      bool   `json:"is_ban"`
	BranchName string `json:"branch_name"`
}

func (q *Queries) ValidateDeviceDetails(ctx context.Context, arg ValidateDeviceDetailsParams) ([]ValidateDeviceDetailsRow, error) {
	rows, err := q.db.QueryContext(ctx, validateDeviceDetails, arg.ApiKey, arg.BranchID, arg.ID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ValidateDeviceDetailsRow
	for rows.Next() {
		var i ValidateDeviceDetailsRow
		if err := rows.Scan(
			&i.ID,
			&i.DeviceName,
			&i.Location,
			&i.IsOnline,
			&i.IsBan,
			&i.BranchName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
