// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0

package database

import (
	"context"
	"database/sql"
)

type Querier interface {
	BanDoorLock(ctx context.Context, id int64) error
	CheckAttendanceRecordExists(ctx context.Context, arg CheckAttendanceRecordExistsParams) (bool, error)
	CheckUserExists(ctx context.Context, userID int64) (bool, error)
	// 9: page offset
	CountAttendees(ctx context.Context, arg CountAttendeesParams) (int64, error)
	CreateAttendee(ctx context.Context, arg CreateAttendeeParams) error
	DeleteDoorLock(ctx context.Context, id int64) error
	GenApiKeyForDoorLock(ctx context.Context, arg GenApiKeyForDoorLockParams) (sql.Result, error)
	GenerateSessionKey(ctx context.Context, arg GenerateSessionKeyParams) (sql.Result, error)
	GetAllAttendee(ctx context.Context) ([]GetAllAttendeeRow, error)
	GetAllDevices(ctx context.Context) ([]GetAllDevicesRow, error)
	GetApiKey(ctx context.Context, id int64) (string, error)
	GetAttendanceRecordsByDate(ctx context.Context, time sql.NullTime) ([]Attendee, error)
	GetAttendanceRecordsByDateRange(ctx context.Context) ([]Attendee, error)
	GetAttendanceRecordsByDateRangePaginated(ctx context.Context, arg GetAttendanceRecordsByDateRangePaginatedParams) ([]Attendee, error)
	GetAttendeeByAttendeeID(ctx context.Context, attendeeID int64) (GetAttendeeByAttendeeIDRow, error)
	GetAttendeeByAttendeeIDAndState(ctx context.Context, arg GetAttendeeByAttendeeIDAndStateParams) (GetAttendeeByAttendeeIDAndStateRow, error)
	GetAttendeeByUserIdAndState(ctx context.Context, arg GetAttendeeByUserIdAndStateParams) (GetAttendeeByUserIdAndStateRow, error)
	GetAttendeeIDByUserID(ctx context.Context, userID int64) (int64, error)
	GetAttendeesByBranchWithPagination(ctx context.Context, arg GetAttendeesByBranchWithPaginationParams) ([]GetAttendeesByBranchWithPaginationRow, error)
	GetBranchCount(ctx context.Context) (int64, error)
	GetDeviceCountByBranch(ctx context.Context) ([]GetDeviceCountByBranchRow, error)
	GetDeviceData(ctx context.Context, id int64) (GetDeviceDataRow, error)
	GetDevicesByBranch(ctx context.Context, branchID int64) ([]GetDevicesByBranchRow, error)
	GetDevicesCount(ctx context.Context) (int64, error)
	GetDoorLockUser(ctx context.Context, branchID int64) ([]GetDoorLockUserRow, error)
	GetOnlineDeviceCount(ctx context.Context) (int64, error)
	GetSessionKey(ctx context.Context, id int64) (sql.NullString, error)
	GetTotalDeviceCount(ctx context.Context) (int64, error)
	GetUserState(ctx context.Context, userID int64) (sql.NullString, error)
	InsertDoorLockUser(ctx context.Context, arg InsertDoorLockUserParams) error
	IsOnlineDoorLock(ctx context.Context, id int64) (bool, error)
	RegisterDoorLock(ctx context.Context, arg RegisterDoorLockParams) (sql.Result, error)
	SetOnlineDoorLock(ctx context.Context, arg SetOnlineDoorLockParams) (sql.Result, error)
	UpdateDoorLock(ctx context.Context, arg UpdateDoorLockParams) (sql.Result, error)
	UpdateDoorOnlineStatus(ctx context.Context, arg UpdateDoorOnlineStatusParams) error
	UpdateUserState(ctx context.Context, arg UpdateUserStateParams) error
	ValidateDeviceDetails(ctx context.Context, arg ValidateDeviceDetailsParams) ([]ValidateDeviceDetailsRow, error)
}

var _ Querier = (*Queries)(nil)
