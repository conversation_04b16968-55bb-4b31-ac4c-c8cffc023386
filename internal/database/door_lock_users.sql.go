// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: door_lock_users.sql

package database

import (
	"context"
	"database/sql"
)

const checkUserExists = `-- name: CheckUserExists :one
SELECT EXISTS (
    SELECT 1 
    FROM door_lock_users 
    WHERE user_id = ?
) AS user_exists
`

func (q *Queries) CheckUserExists(ctx context.Context, userID int64) (bool, error) {
	row := q.db.QueryRowContext(ctx, checkUserExists, userID)
	var user_exists bool
	err := row.Scan(&user_exists)
	return user_exists, err
}

const getAttendeeIDByUserID = `-- name: GetAttendeeIDByUserID :one
SELECT attendee_id
FROM door_lock_users
WHERE user_id = ?
`

func (q *Queries) GetAttendeeIDByUserID(ctx context.Context, userID int64) (int64, error) {
	row := q.db.QueryRowContext(ctx, getAttendeeIDByUserID, userID)
	var attendee_id int64
	err := row.Scan(&attendee_id)
	return attendee_id, err
}

const getDoorLockUser = `-- name: GetDoorLockUser :many
SELECT
  attendee_id,
  user_id,
  username,
  subscription_id,
  last_in,
  last_out,
  current_state,
  created_at,
  updated_at,
  email,
  role,
  phone,
  nic,
  avatar_url
FROM door_lock_users
WHERE branch_id = ?
`

type GetDoorLockUserRow struct {
	AttendeeID     int64          `json:"attendee_id"`
	UserID         int64          `json:"user_id"`
	Username       string         `json:"username"`
	SubscriptionID sql.NullInt64  `json:"subscription_id"`
	LastIn         sql.NullTime   `json:"last_in"`
	LastOut        sql.NullTime   `json:"last_out"`
	CurrentState   sql.NullString `json:"current_state"`
	CreatedAt      sql.NullTime   `json:"created_at"`
	UpdatedAt      sql.NullTime   `json:"updated_at"`
	Email          sql.NullString `json:"email"`
	Role           sql.NullString `json:"role"`
	Phone          sql.NullString `json:"phone"`
	Nic            sql.NullString `json:"nic"`
	AvatarUrl      sql.NullString `json:"avatar_url"`
}

func (q *Queries) GetDoorLockUser(ctx context.Context, branchID int64) ([]GetDoorLockUserRow, error) {
	rows, err := q.db.QueryContext(ctx, getDoorLockUser, branchID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetDoorLockUserRow
	for rows.Next() {
		var i GetDoorLockUserRow
		if err := rows.Scan(
			&i.AttendeeID,
			&i.UserID,
			&i.Username,
			&i.SubscriptionID,
			&i.LastIn,
			&i.LastOut,
			&i.CurrentState,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Email,
			&i.Role,
			&i.Phone,
			&i.Nic,
			&i.AvatarUrl,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserState = `-- name: GetUserState :one
SELECT current_state 
FROM door_lock_users 
WHERE user_id = ?
`

func (q *Queries) GetUserState(ctx context.Context, userID int64) (sql.NullString, error) {
	row := q.db.QueryRowContext(ctx, getUserState, userID)
	var current_state sql.NullString
	err := row.Scan(&current_state)
	return current_state, err
}

const insertDoorLockUser = `-- name: InsertDoorLockUser :exec
INSERT INTO door_lock_users (
  user_id,
  username,
  branch_id,
  email,
  role,
  phone,
  nic,
  avatar_url
) VALUES (
  ?, ?, ?, ?, ?, ?, ?, ?
)
`

type InsertDoorLockUserParams struct {
	UserID    int64          `json:"user_id"`
	Username  string         `json:"username"`
	BranchID  int64          `json:"branch_id"`
	Email     sql.NullString `json:"email"`
	Role      sql.NullString `json:"role"`
	Phone     sql.NullString `json:"phone"`
	Nic       sql.NullString `json:"nic"`
	AvatarUrl sql.NullString `json:"avatar_url"`
}

func (q *Queries) InsertDoorLockUser(ctx context.Context, arg InsertDoorLockUserParams) error {
	_, err := q.db.ExecContext(ctx, insertDoorLockUser,
		arg.UserID,
		arg.Username,
		arg.BranchID,
		arg.Email,
		arg.Role,
		arg.Phone,
		arg.Nic,
		arg.AvatarUrl,
	)
	return err
}

const updateUserState = `-- name: UpdateUserState :exec
UPDATE door_lock_users 
SET current_state = ?,
    updated_at = CURRENT_TIMESTAMP
WHERE user_id = ?
`

type UpdateUserStateParams struct {
	CurrentState sql.NullString `json:"current_state"`
	UserID       int64          `json:"user_id"`
}

func (q *Queries) UpdateUserState(ctx context.Context, arg UpdateUserStateParams) error {
	_, err := q.db.ExecContext(ctx, updateUserState, arg.CurrentState, arg.UserID)
	return err
}
