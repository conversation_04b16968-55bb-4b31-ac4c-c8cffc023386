// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0

package database

import (
	"database/sql"
)

type Attendee struct {
	ID           int64          `json:"id"`
	AttendeeID   int64          `json:"attendee_id"`
	Time         sql.NullTime   `json:"time"`
	State        string         `json:"state"`
	BranchID     int64          `json:"branch_id"`
	DeviceID     sql.NullInt64  `json:"device_id"`
	Subscription sql.NullString `json:"subscription"`
	Booktype     sql.NullString `json:"booktype"`
	ServiceData  sql.NullString `json:"service_data"`
	GymData      sql.NullString `json:"gym_data"`
}

type DoorLockUser struct {
	UserID         int64          `json:"user_id"`
	BranchID       int64          `json:"branch_id"`
	LastIn         sql.NullTime   `json:"last_in"`
	LastOut        sql.NullTime   `json:"last_out"`
	CurrentState   sql.NullString `json:"current_state"`
	CreatedAt      sql.NullTime   `json:"created_at"`
	UpdatedAt      sql.NullTime   `json:"updated_at"`
	Username       string         `json:"username"`
	AttendeeID     int64          `json:"attendee_id"`
	Email          sql.NullString `json:"email"`
	Role           sql.NullString `json:"role"`
	Phone          sql.NullString `json:"phone"`
	Nic            sql.NullString `json:"nic"`
	AvatarUrl      sql.NullString `json:"avatar_url"`
	SubscriptionID sql.NullInt64  `json:"subscription_id"`
}

type HrBranch struct {
	ID        int64        `json:"id"`
	Name      string       `json:"name"`
	CreatedAt sql.NullTime `json:"created_at"`
}

type RegDoorLock struct {
	ID             int64          `json:"id"`
	DeviceName     string         `json:"device_name"`
	Location       string         `json:"location"`
	BranchID       int64          `json:"branch_id"`
	ApiKey         string         `json:"api_key"`
	Meta           sql.NullString `json:"meta"`
	SessionKey     sql.NullString `json:"session_key"`
	LastSessionKey sql.NullTime   `json:"last_session_key"`
	IsOnline       bool           `json:"is_online"`
	IsBan          bool           `json:"is_ban"`
	LastOnline     sql.NullTime   `json:"last_online"`
	CreatedAt      sql.NullTime   `json:"created_at"`
	UpdatedAt      sql.NullTime   `json:"updated_at"`
}
