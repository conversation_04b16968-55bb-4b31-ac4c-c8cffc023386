// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: attendee.sql

package database

import (
	"context"
	"database/sql"
)

const checkAttendanceRecordExists = `-- name: CheckAttendanceRecordExists :one
SELECT EXISTS (
    SELECT 1
    FROM door_lock_users d
    JOIN attendee a ON d.attendee_id = a.attendee_id
    WHERE d.user_id = ?
      AND a.state = ?
      AND DATE(a.time) = ?
      AND a.booktype = ?
) AS record_exists
`

type CheckAttendanceRecordExistsParams struct {
	UserID   int64          `json:"user_id"`
	State    string         `json:"state"`
	Time     sql.NullTime   `json:"time"`
	Booktype sql.NullString `json:"booktype"`
}

func (q *Queries) CheckAttendanceRecordExists(ctx context.Context, arg CheckAttendanceRecordExistsParams) (bool, error) {
	row := q.db.QueryRowContext(ctx, checkAttendanceRecordExists,
		arg.UserID,
		arg.State,
		arg.Time,
		arg.Booktype,
	)
	var record_exists bool
	err := row.Scan(&record_exists)
	return record_exists, err
}

const countAttendees = `-- name: CountAttendees :one


SELECT COUNT(*) 
FROM attendee AS a
WHERE a.branch_id = ?
  AND (? = '' OR a.state = ?)
  AND DATE(a.time) = ?
`

type CountAttendeesParams struct {
	BranchID int64        `json:"branch_id"`
	Column2  interface{}  `json:"column_2"`
	State    string       `json:"state"`
	Time     sql.NullTime `json:"time"`
}

// 9: page offset
func (q *Queries) CountAttendees(ctx context.Context, arg CountAttendeesParams) (int64, error) {
	row := q.db.QueryRowContext(ctx, countAttendees,
		arg.BranchID,
		arg.Column2,
		arg.State,
		arg.Time,
	)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createAttendee = `-- name: CreateAttendee :exec
INSERT INTO attendee (
  attendee_id,
  state,
  branch_id,
  device_id,
  subscription,
  booktype,
  service_data,
  gym_data
) VALUES (
  ?,  -- 1: attendee_id
  ?,  -- 2: state
  ?,  -- 3: branch_id
  ?,  -- 4: device_id
  ?,  -- 5: subscription (e.g. "monthly")
  ?,  -- 6: booktype (e.g. "service")
  ?,  -- 7: service_data (JSON string or NULL)
  ?   -- 8: gym_data     (JSON string or NULL)
)
`

type CreateAttendeeParams struct {
	AttendeeID   int64          `json:"attendee_id"`
	State        string         `json:"state"`
	BranchID     int64          `json:"branch_id"`
	DeviceID     sql.NullInt64  `json:"device_id"`
	Subscription sql.NullString `json:"subscription"`
	Booktype     sql.NullString `json:"booktype"`
	ServiceData  sql.NullString `json:"service_data"`
	GymData      sql.NullString `json:"gym_data"`
}

func (q *Queries) CreateAttendee(ctx context.Context, arg CreateAttendeeParams) error {
	_, err := q.db.ExecContext(ctx, createAttendee,
		arg.AttendeeID,
		arg.State,
		arg.BranchID,
		arg.DeviceID,
		arg.Subscription,
		arg.Booktype,
		arg.ServiceData,
		arg.GymData,
	)
	return err
}

const getAllAttendee = `-- name: GetAllAttendee :many
SELECT
  a.id AS id,
  a.state,
  a.time,
  u.user_id AS user_id,
  u.username AS user_name,
  b.id AS branch_id,
  b.name AS branch_name,
  r.device_name AS device_name
FROM attendee a
JOIN door_lock_users u ON a.attendee_id = u.attendee_id
JOIN HR_Branch b ON a.branch_id = b.id
JOIN reg_door_lock r ON a.device_id = r.id
`

type GetAllAttendeeRow struct {
	ID         int64        `json:"id"`
	State      string       `json:"state"`
	Time       sql.NullTime `json:"time"`
	UserID     int64        `json:"user_id"`
	UserName   string       `json:"user_name"`
	BranchID   int64        `json:"branch_id"`
	BranchName string       `json:"branch_name"`
	DeviceName string       `json:"device_name"`
}

func (q *Queries) GetAllAttendee(ctx context.Context) ([]GetAllAttendeeRow, error) {
	rows, err := q.db.QueryContext(ctx, getAllAttendee)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllAttendeeRow
	for rows.Next() {
		var i GetAllAttendeeRow
		if err := rows.Scan(
			&i.ID,
			&i.State,
			&i.Time,
			&i.UserID,
			&i.UserName,
			&i.BranchID,
			&i.BranchName,
			&i.DeviceName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAttendanceRecordsByDate = `-- name: GetAttendanceRecordsByDate :many
SELECT id, attendee_id, time, state, branch_id, device_id, subscription, booktype, service_data, gym_data
FROM attendee
WHERE DATE(` + "`" + `time` + "`" + `) = ?
`

func (q *Queries) GetAttendanceRecordsByDate(ctx context.Context, time sql.NullTime) ([]Attendee, error) {
	rows, err := q.db.QueryContext(ctx, getAttendanceRecordsByDate, time)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Attendee
	for rows.Next() {
		var i Attendee
		if err := rows.Scan(
			&i.ID,
			&i.AttendeeID,
			&i.Time,
			&i.State,
			&i.BranchID,
			&i.DeviceID,
			&i.Subscription,
			&i.Booktype,
			&i.ServiceData,
			&i.GymData,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAttendanceRecordsByDateRange = `-- name: GetAttendanceRecordsByDateRange :many
SELECT id, attendee_id, time, state, branch_id, device_id, subscription, booktype, service_data, gym_data
FROM attendee
WHERE DATE(` + "`" + `time` + "`" + `) BETWEEN ? AND ?
`

func (q *Queries) GetAttendanceRecordsByDateRange(ctx context.Context) ([]Attendee, error) {
	rows, err := q.db.QueryContext(ctx, getAttendanceRecordsByDateRange)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Attendee
	for rows.Next() {
		var i Attendee
		if err := rows.Scan(
			&i.ID,
			&i.AttendeeID,
			&i.Time,
			&i.State,
			&i.BranchID,
			&i.DeviceID,
			&i.Subscription,
			&i.Booktype,
			&i.ServiceData,
			&i.GymData,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAttendanceRecordsByDateRangePaginated = `-- name: GetAttendanceRecordsByDateRangePaginated :many
SELECT id, attendee_id, time, state, branch_id, device_id, subscription, booktype, service_data, gym_data
FROM attendee
WHERE DATE(` + "`" + `time` + "`" + `) BETWEEN ? AND ?
ORDER BY ` + "`" + `time` + "`" + ` DESC
LIMIT ? OFFSET ?
`

type GetAttendanceRecordsByDateRangePaginatedParams struct {
	Limit  int32 `json:"limit"`
	Offset int32 `json:"offset"`
}

func (q *Queries) GetAttendanceRecordsByDateRangePaginated(ctx context.Context, arg GetAttendanceRecordsByDateRangePaginatedParams) ([]Attendee, error) {
	rows, err := q.db.QueryContext(ctx, getAttendanceRecordsByDateRangePaginated, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Attendee
	for rows.Next() {
		var i Attendee
		if err := rows.Scan(
			&i.ID,
			&i.AttendeeID,
			&i.Time,
			&i.State,
			&i.BranchID,
			&i.DeviceID,
			&i.Subscription,
			&i.Booktype,
			&i.ServiceData,
			&i.GymData,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAttendeeByAttendeeID = `-- name: GetAttendeeByAttendeeID :one
SELECT
  a.id AS id,
  a.state,
  a.time,
  u.user_id AS user_id,
  u.username AS user_name,
  b.id AS branch_id,
  b.name AS branch_name,
  r.device_name AS device_name
FROM attendee a
JOIN door_lock_users u ON a.attendee_id = u.attendee_id
JOIN HR_Branch b ON a.branch_id = b.id
JOIN reg_door_lock r ON a.device_id = r.id
WHERE a.attendee_id = ?
`

type GetAttendeeByAttendeeIDRow struct {
	ID         int64        `json:"id"`
	State      string       `json:"state"`
	Time       sql.NullTime `json:"time"`
	UserID     int64        `json:"user_id"`
	UserName   string       `json:"user_name"`
	BranchID   int64        `json:"branch_id"`
	BranchName string       `json:"branch_name"`
	DeviceName string       `json:"device_name"`
}

func (q *Queries) GetAttendeeByAttendeeID(ctx context.Context, attendeeID int64) (GetAttendeeByAttendeeIDRow, error) {
	row := q.db.QueryRowContext(ctx, getAttendeeByAttendeeID, attendeeID)
	var i GetAttendeeByAttendeeIDRow
	err := row.Scan(
		&i.ID,
		&i.State,
		&i.Time,
		&i.UserID,
		&i.UserName,
		&i.BranchID,
		&i.BranchName,
		&i.DeviceName,
	)
	return i, err
}

const getAttendeeByAttendeeIDAndState = `-- name: GetAttendeeByAttendeeIDAndState :one
SELECT
  a.id AS id,
  a.state,
  a.time,
  u.user_id AS user_id,
  u.username AS user_name,
  b.id AS branch_id,
  b.name AS branch_name
FROM attendee a
JOIN door_lock_users u ON a.attendee_id = u.attendee_id
JOIN HR_Branch b ON a.branch_id = b.id
WHERE a.attendee_id = ? AND a.state = ?
`

type GetAttendeeByAttendeeIDAndStateParams struct {
	AttendeeID int64  `json:"attendee_id"`
	State      string `json:"state"`
}

type GetAttendeeByAttendeeIDAndStateRow struct {
	ID         int64        `json:"id"`
	State      string       `json:"state"`
	Time       sql.NullTime `json:"time"`
	UserID     int64        `json:"user_id"`
	UserName   string       `json:"user_name"`
	BranchID   int64        `json:"branch_id"`
	BranchName string       `json:"branch_name"`
}

func (q *Queries) GetAttendeeByAttendeeIDAndState(ctx context.Context, arg GetAttendeeByAttendeeIDAndStateParams) (GetAttendeeByAttendeeIDAndStateRow, error) {
	row := q.db.QueryRowContext(ctx, getAttendeeByAttendeeIDAndState, arg.AttendeeID, arg.State)
	var i GetAttendeeByAttendeeIDAndStateRow
	err := row.Scan(
		&i.ID,
		&i.State,
		&i.Time,
		&i.UserID,
		&i.UserName,
		&i.BranchID,
		&i.BranchName,
	)
	return i, err
}

const getAttendeeByUserIdAndState = `-- name: GetAttendeeByUserIdAndState :one
SELECT
  a.id AS id,
  a.state,
  a.time,
  u.user_id AS user_id,
  u.username AS user_name,
  b.id AS branch_id,
  b.name AS branch_name
FROM attendee a
JOIN door_lock_users u ON a.attendee_id = u.attendee_id
JOIN HR_Branch b ON a.branch_id = b.id
WHERE u.user_id = ? AND a.state = ?
`

type GetAttendeeByUserIdAndStateParams struct {
	UserID int64  `json:"user_id"`
	State  string `json:"state"`
}

type GetAttendeeByUserIdAndStateRow struct {
	ID         int64        `json:"id"`
	State      string       `json:"state"`
	Time       sql.NullTime `json:"time"`
	UserID     int64        `json:"user_id"`
	UserName   string       `json:"user_name"`
	BranchID   int64        `json:"branch_id"`
	BranchName string       `json:"branch_name"`
}

func (q *Queries) GetAttendeeByUserIdAndState(ctx context.Context, arg GetAttendeeByUserIdAndStateParams) (GetAttendeeByUserIdAndStateRow, error) {
	row := q.db.QueryRowContext(ctx, getAttendeeByUserIdAndState, arg.UserID, arg.State)
	var i GetAttendeeByUserIdAndStateRow
	err := row.Scan(
		&i.ID,
		&i.State,
		&i.Time,
		&i.UserID,
		&i.UserName,
		&i.BranchID,
		&i.BranchName,
	)
	return i, err
}

const getAttendeesByBranchWithPagination = `-- name: GetAttendeesByBranchWithPagination :many
SELECT
  a.id,
  a.attendee_id,
  a.time,
  a.state,
  a.branch_id,
  b.name           AS branch_name,
  a.device_id,
  u.user_id,
  u.subscription_id,
  u.last_in,
  u.last_out,
  u.current_state,
  u.username,
  u.email,
  u.role,
  u.phone,
  u.nic,
  u.avatar_url,
  u.created_at    AS user_created_at,
  u.updated_at    AS user_updated_at,
  a.booktype,
  a.service_data,
  a.gym_data
FROM attendee AS a
JOIN door_lock_users AS u
  ON a.attendee_id = u.attendee_id
JOIN HR_Branch AS b
  ON a.branch_id = b.id
WHERE a.branch_id =         ?   -- 1: branch filter
  AND a.device_id =         ?   -- 2: device filter
  AND (? = '' OR a.state = ?)   -- 3,4: optional state filter
  AND DATE(a.time) =        ?   -- 5: date filter
ORDER BY
  a.state ASC,
  (CASE WHEN ? = 'asc'  THEN a.time END) ASC,  -- 6: pass 'asc' or any other for no‐op
  (CASE WHEN ? = 'desc' THEN a.time END) DESC  -- 7: pass 'desc' or any other for no‐op
LIMIT  ?                                       -- 8: page size
OFFSET ?
`

type GetAttendeesByBranchWithPaginationParams struct {
	BranchID int64         `json:"branch_id"`
	DeviceID sql.NullInt64 `json:"device_id"`
	Column3  interface{}   `json:"column_3"`
	State    string        `json:"state"`
	Time     sql.NullTime  `json:"time"`
	Column6  interface{}   `json:"column_6"`
	Column7  interface{}   `json:"column_7"`
	Limit    int32         `json:"limit"`
	Offset   int32         `json:"offset"`
}

type GetAttendeesByBranchWithPaginationRow struct {
	ID             int64          `json:"id"`
	AttendeeID     int64          `json:"attendee_id"`
	Time           sql.NullTime   `json:"time"`
	State          string         `json:"state"`
	BranchID       int64          `json:"branch_id"`
	BranchName     string         `json:"branch_name"`
	DeviceID       sql.NullInt64  `json:"device_id"`
	UserID         int64          `json:"user_id"`
	SubscriptionID sql.NullInt64  `json:"subscription_id"`
	LastIn         sql.NullTime   `json:"last_in"`
	LastOut        sql.NullTime   `json:"last_out"`
	CurrentState   sql.NullString `json:"current_state"`
	Username       string         `json:"username"`
	Email          sql.NullString `json:"email"`
	Role           sql.NullString `json:"role"`
	Phone          sql.NullString `json:"phone"`
	Nic            sql.NullString `json:"nic"`
	AvatarUrl      sql.NullString `json:"avatar_url"`
	UserCreatedAt  sql.NullTime   `json:"user_created_at"`
	UserUpdatedAt  sql.NullTime   `json:"user_updated_at"`
	Booktype       sql.NullString `json:"booktype"`
	ServiceData    sql.NullString `json:"service_data"`
	GymData        sql.NullString `json:"gym_data"`
}

func (q *Queries) GetAttendeesByBranchWithPagination(ctx context.Context, arg GetAttendeesByBranchWithPaginationParams) ([]GetAttendeesByBranchWithPaginationRow, error) {
	rows, err := q.db.QueryContext(ctx, getAttendeesByBranchWithPagination,
		arg.BranchID,
		arg.DeviceID,
		arg.Column3,
		arg.State,
		arg.Time,
		arg.Column6,
		arg.Column7,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAttendeesByBranchWithPaginationRow
	for rows.Next() {
		var i GetAttendeesByBranchWithPaginationRow
		if err := rows.Scan(
			&i.ID,
			&i.AttendeeID,
			&i.Time,
			&i.State,
			&i.BranchID,
			&i.BranchName,
			&i.DeviceID,
			&i.UserID,
			&i.SubscriptionID,
			&i.LastIn,
			&i.LastOut,
			&i.CurrentState,
			&i.Username,
			&i.Email,
			&i.Role,
			&i.Phone,
			&i.Nic,
			&i.AvatarUrl,
			&i.UserCreatedAt,
			&i.UserUpdatedAt,
			&i.Booktype,
			&i.ServiceData,
			&i.GymData,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
