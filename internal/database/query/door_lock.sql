-- name: RegisterDoorLock :execresult 
INSERT INTO reg_door_lock (
     branch_id, device_name, location, api_key, meta
) VALUES (?, ?, ?, ?, ?);

-- name: GenApiKeyForDoorLock :execresult
UPDATE reg_door_lock 
SET api_key = ?, updated_at = CURRENT_TIMESTAMP
WHERE id = ?;

-- name: UpdateDoorOnlineStatus :exec
UPDATE reg_door_lock
SET 
  is_online = ?, 
  last_online = CURRENT_TIMESTAMP
WHERE id = ?;


-- name: GenerateSessionKey :execresult
UPDATE reg_door_lock 
SET session_key = ?, last_session_key = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP 
WHERE id = ?;

-- name: UpdateDoorLock :execresult
UPDATE reg_door_lock 
SET device_name = ?, location = ?, meta = ?, updated_at = CURRENT_TIMESTAMP 
WHERE id = ?;

-- name: DeleteDoorLock :exec
DELETE FROM reg_door_lock 
WHERE id = ?;

-- name: BanDoorLock :exec
UPDATE reg_door_lock 
SET is_ban = TRUE, updated_at = CURRENT_TIMESTAMP 
WHERE id = ?;

-- name: GetDevicesCount :one
SELECT COUNT(*) AS total_count FROM reg_door_lock;

-- name: GetBranchCount :one
SELECT COUNT(*) AS total_count FROM HR_Branch;


-- name: GetDeviceData :one
SELECT d.id, d.device_name, d.location, d.branch_id, b.name AS branch_name, 
       d.meta, d.is_online, d.is_ban, d.last_online, d.created_at, d.updated_at
FROM reg_door_lock d
JOIN HR_Branch b ON d.branch_id = b.id
WHERE d.id = ?;

-- name: GetApiKey :one
SELECT api_key 
FROM reg_door_lock 
WHERE id = ?;

-- name: GetSessionKey :one
SELECT session_key 
FROM reg_door_lock 
WHERE id = ?;

-- name: IsOnlineDoorLock :one
SELECT is_online 
FROM reg_door_lock 
WHERE id = ?;

-- name: SetOnlineDoorLock :execresult
UPDATE reg_door_lock 
SET is_online = ?, last_online = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP 
WHERE id = ?;

-- name: GetDevicesByBranch :many
SELECT d.id, d.device_name, d.location, d.branch_id, b.name AS branch_name, 
       d.meta, d.is_online, d.is_ban, d.last_online, d.created_at, d.updated_at
FROM reg_door_lock d
JOIN HR_Branch b ON d.branch_id = b.id
WHERE d.branch_id = ?
ORDER BY d.id;

-- name: GetAllDevices :many
SELECT d.id, d.device_name, d.location, d.branch_id, b.name AS branch_name, 
       d.meta, d.is_online, d.is_ban, d.last_online, d.created_at, d.updated_at
FROM reg_door_lock d
JOIN HR_Branch b ON d.branch_id = b.id
ORDER BY d.branch_id, d.id;

-- name: GetTotalDeviceCount :one
SELECT COUNT(*) AS total_devices
FROM reg_door_lock;

-- name: GetOnlineDeviceCount :one
SELECT COUNT(*) AS online_devices
FROM reg_door_lock
WHERE is_online = TRUE AND is_ban = FALSE;

-- name: GetDeviceCountByBranch :many
SELECT b.id AS branch_id, b.name AS branch_name, COUNT(d.id) AS device_count
FROM HR_Branch b
LEFT JOIN reg_door_lock d ON b.id = d.branch_id
GROUP BY b.id, b.name
ORDER BY b.name;

-- name: ValidateDeviceDetails :many
SELECT 
    dl.id,
    dl.device_name,
    dl.location,
    dl.is_online,
    dl.is_ban,
    b.name as branch_name
FROM 
    reg_door_lock dl
JOIN 
    HR_Branch b ON dl.branch_id = b.id
WHERE 
    dl.api_key = ?
    AND dl.branch_id = ?
    AND dl.id = ?
    AND dl.is_ban = FALSE;
