-- name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> :exec
INSERT INTO attendee (
  attendee_id,
  state,
  branch_id,
  device_id,
  subscription,
  booktype,
  service_data,
  gym_data
) VALUES (
  ?,  -- 1: attendee_id
  ?,  -- 2: state
  ?,  -- 3: branch_id
  ?,  -- 4: device_id
  ?,  -- 5: subscription (e.g. "monthly")
  ?,  -- 6: booktype (e.g. "service")
  ?,  -- 7: service_data (JSON string or NULL)
  ?   -- 8: gym_data     (JSON string or NULL)
);

-- name: GetAllAttendee :many
SELECT
  a.id AS id,
  a.state,
  a.time,
  u.user_id AS user_id,
  u.username AS user_name,
  b.id AS branch_id,
  b.name AS branch_name,
  r.device_name AS device_name
FROM attendee a
JOIN door_lock_users u ON a.attendee_id = u.attendee_id
JOIN HR_Branch b ON a.branch_id = b.id
JOIN reg_door_lock r ON a.device_id = r.id;

-- name: GetAttendeeByAttendeeID :one
SELECT
  a.id AS id,
  a.state,
  a.time,
  u.user_id AS user_id,
  u.username AS user_name,
  b.id AS branch_id,
  b.name AS branch_name,
  r.device_name AS device_name
FROM attendee a
JOIN door_lock_users u ON a.attendee_id = u.attendee_id
JOIN HR_Branch b ON a.branch_id = b.id
JOIN reg_door_lock r ON a.device_id = r.id
WHERE a.attendee_id = ?;

-- name: GetAttendeeByAttendeeIDAndState :one
SELECT
  a.id AS id,
  a.state,
  a.time,
  u.user_id AS user_id,
  u.username AS user_name,
  b.id AS branch_id,
  b.name AS branch_name
FROM attendee a
JOIN door_lock_users u ON a.attendee_id = u.attendee_id
JOIN HR_Branch b ON a.branch_id = b.id
WHERE a.attendee_id = ? AND a.state = ?;


-- name: GetAttendeeByUserIdAndState :one
SELECT
  a.id AS id,
  a.state,
  a.time,
  u.user_id AS user_id,
  u.username AS user_name,
  b.id AS branch_id,
  b.name AS branch_name
FROM attendee a
JOIN door_lock_users u ON a.attendee_id = u.attendee_id
JOIN HR_Branch b ON a.branch_id = b.id
WHERE u.user_id = ? AND a.state = ?;

-- name: GetAttendanceRecordsByDateRange :many
SELECT *
FROM attendee
WHERE DATE(`time`) BETWEEN ? AND ?;

-- name: GetAttendanceRecordsByDate :many
SELECT *
FROM attendee
WHERE DATE(`time`) = ?;

-- name: GetAttendanceRecordsByDateRangePaginated :many
SELECT *
FROM attendee
WHERE DATE(`time`) BETWEEN ? AND ?
ORDER BY `time` DESC
LIMIT ? OFFSET ?;


-- name: CheckAttendanceRecordExists :one
SELECT EXISTS (
    SELECT 1
    FROM door_lock_users d
    JOIN attendee a ON d.attendee_id = a.attendee_id
    WHERE d.user_id = ?
      AND a.state = ?
      AND DATE(a.time) = ?
      AND a.booktype = ?
) AS record_exists;


-- name: GetAttendeesByBranchWithPagination :many
SELECT
  a.id,
  a.attendee_id,
  a.time,
  a.state,
  a.branch_id,
  b.name           AS branch_name,
  a.device_id,
  u.user_id,
  u.subscription_id,
  u.last_in,
  u.last_out,
  u.current_state,
  u.username,
  u.email,
  u.role,
  u.phone,
  u.nic,
  u.avatar_url,
  u.created_at    AS user_created_at,
  u.updated_at    AS user_updated_at,
  a.booktype,
  a.service_data,
  a.gym_data
FROM attendee AS a
JOIN door_lock_users AS u
  ON a.attendee_id = u.attendee_id
JOIN HR_Branch AS b
  ON a.branch_id = b.id
WHERE a.branch_id =         ?   -- 1: branch filter
  AND a.device_id =         ?   -- 2: device filter
  AND (? = '' OR a.state = ?)   -- 3,4: optional state filter
  AND DATE(a.time) =        ?   -- 5: date filter
ORDER BY
  a.state ASC,
  (CASE WHEN ? = 'asc'  THEN a.time END) ASC,  -- 6: pass 'asc' or any other for no‐op
  (CASE WHEN ? = 'desc' THEN a.time END) DESC  -- 7: pass 'desc' or any other for no‐op
LIMIT  ?                                       -- 8: page size
OFFSET ?;                                     -- 9: page offset


-- name: CountAttendees :one
SELECT COUNT(*) 
FROM attendee AS a
WHERE a.branch_id = ?
  AND (? = '' OR a.state = ?)
  AND DATE(a.time) = ?;  -- New condition: count only records for the specified date
