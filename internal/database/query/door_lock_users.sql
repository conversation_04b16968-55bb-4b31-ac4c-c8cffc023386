-- name: Inser<PERSON><PERSON>oorLockUser :exec
INSERT INTO door_lock_users (
  user_id,
  username,
  branch_id,
  email,
  role,
  phone,
  nic,
  avatar_url
) VALUES (
  ?, ?, ?, ?, ?, ?, ?, ?
);

-- name: GetDoorLockUser :many
SELECT
  attendee_id,
  user_id,
  username,
  subscription_id,
  last_in,
  last_out,
  current_state,
  created_at,
  updated_at,
  email,
  role,
  phone,
  nic,
  avatar_url
FROM door_lock_users
WHERE branch_id = ?;

-- name: GetUserState :one
SELECT current_state 
FROM door_lock_users 
WHERE user_id = ?;

-- name: UpdateUserState :exec
UPDATE door_lock_users 
SET current_state = ?,
    updated_at = CURRENT_TIMESTAMP
WHERE user_id = ?;

-- name: CheckUserExists :one
SELECT EXISTS (
    SELECT 1 
    FROM door_lock_users 
    WHERE user_id = ?
) AS user_exists;

-- name: Get<PERSON><PERSON><PERSON>eIDByUserID :one
SELECT attendee_id
FROM door_lock_users
WHERE user_id = ?;
