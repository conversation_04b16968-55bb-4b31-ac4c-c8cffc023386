package http

import (
	"database/sql"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
)

type CreateDoorLock struct {
	BranchID   int64          `json:"branch_id"`
	DeviceName string         `json:"device_name"`
	Location   string         `json:"location"`
	Meta       string         `json:"meta"`
}

func (S *CreateDoorLock) convertToDB(hashed_key string) (*database.RegisterDoorLockParams, error) {
	var meta sql.NullString
	meta.String = S.Meta
	meta.Valid = true
	
	return &database.RegisterDoorLockParams{
		BranchID:   S.BranchID,
		DeviceName: S.DeviceName,
		Location:   S.Location,
		ApiKey:     hashed_key,
		Meta:       meta,
	}, nil
}

type UpdateDoorLock struct {
	DeviceName string         `json:"device_name"`
	Location   string         `json:"location"`
	Meta       string         `json:"meta"`
}

func (S *UpdateDoorLock) convertToUpdateDB(id int64) (*database.UpdateDoorLockParams, error) {
	var meta sql.NullString
	meta.String = S.Meta
	meta.Valid = true
	
	return &database.UpdateDoorLockParams{
		DeviceName: S.Device<PERSON>ame,
		Location:   S.Location,
		Meta:       meta,
		ID:         id,
	}, nil
}

type UpdateDoorOnlineStatus struct {
	IsOnline bool  `json:"is_online"`
	ID       int64 `json:"id"`
}

func (S *UpdateDoorOnlineStatus) convertToDB(id int64) (*database.UpdateDoorOnlineStatusParams, error) {
	return &database.UpdateDoorOnlineStatusParams{
		IsOnline: S.IsOnline,
		ID:       id,
	}, nil
}