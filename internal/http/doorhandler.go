package http

import (
	"crypto/rand"
	"encoding/base64"
	"net/http"
	"strconv"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/labstack/echo/v4"
)

func (H *HttpRoutes) CreateDevice(c echo.Context) error {
	var req CreateDoorLock
	err := c.Bind(&req)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	keyBytes := make([]byte, 32)
	_, err = rand.Read(keyBytes)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, err.Error())
	}
	apiKey := base64.URLEncoding.EncodeToString(keyBytes)
	hashedKey := utils.HashAPIKey(apiKey)

	params, err := req.convertToDB(hashedKey)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	result, err := H.query.RegisterDoorLock(c.Request().Context(), *params)
	if err != nil {
		return c.JSON(500, err.Error())
	}
	lastInsertID, err := result.LastInsertId()
	if err != nil {
		return c.JSON(500, err.Error())
	}
	return c.JSON(http.StatusOK, map[string]interface{}{
		"api_key": apiKey,
		"result":  lastInsertID,
	})
}

func (H *HttpRoutes) DeleteDevice(c echo.Context) error {
	device, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	err = H.query.DeleteDoorLock(c.Request().Context(), device)
	if err != nil {
		return c.JSON(500, err.Error())
	}
	return c.JSON(http.StatusOK, "device deleted successfully")
}

func (H *HttpRoutes) GetDevice(c echo.Context) error {
	devices, err := H.query.GetAllDevices(c.Request().Context())
	if err != nil {
		return c.JSON(500, err.Error())
	}

	for i := range devices {
		strId := strconv.FormatInt(devices[i].ID, 10)
		check := H.hub.CheckClientExistence(strId)
		if check {
			devices[i].IsOnline = true
		} else {
			devices[i].IsOnline = false
		}
	}

	return c.JSON(http.StatusOK, devices)
}

func (H *HttpRoutes) UpdateDevice(c echo.Context) error {
	var req UpdateDoorLock
	err := c.Bind(&req)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	device, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	params, err := req.convertToUpdateDB(device)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	result, err := H.query.UpdateDoorLock(c.Request().Context(), *params)
	if err != nil {
		return c.JSON(500, err.Error())
	}
	return c.JSON(http.StatusOK, result)
}

func (H *HttpRoutes) GetDeviceCount(c echo.Context) error {
	count, err := H.query.GetDevicesCount(c.Request().Context())
	if err != nil {
		return c.JSON(500, err.Error())
	}
	return c.JSON(http.StatusOK, count)
}

func (H *HttpRoutes) GetBranchesCount(c echo.Context) error {
	count, err := H.query.GetBranchCount(c.Request().Context())
	if err != nil {
		return c.JSON(500, err.Error())
	}
	return c.JSON(http.StatusOK, count)
}

func (H *HttpRoutes) GetDevicesByBranch(c echo.Context) error {
	branchId, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	devices, err := H.query.GetDevicesByBranch(c.Request().Context(), branchId)
	if err != nil {
		return c.JSON(500, err.Error())
	}

	for i := range devices {
		strId := strconv.FormatInt(devices[i].ID, 10)
		check := H.hub.CheckClientExistence(strId)
		if check {
			devices[i].IsOnline = true
		} else {
			devices[i].IsOnline = false
		}
	}

	return c.JSON(http.StatusOK, devices)
}

func (H *HttpRoutes) UpdateDeviceStatus(c echo.Context) error {
	var req UpdateDoorOnlineStatus
	err := c.Bind(&req)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	device, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	params, err := req.convertToDB(device)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	err = H.query.UpdateDoorOnlineStatus(c.Request().Context(), *params)
	if err != nil {
		return c.JSON(500, err.Error())
	}
	return c.JSON(http.StatusOK, "device status updated successfully")
}

func (H *HttpRoutes) GetOneDevice(c echo.Context) error {
	deviceID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	device, err := H.query.GetDeviceData(c.Request().Context(), deviceID)
	if err != nil {
		return c.JSON(500, err.Error())
	}

	strId := strconv.FormatInt(device.ID, 10)
	check := H.hub.CheckClientExistence(strId)
	if check {
		device.IsOnline = true
	} else {
		device.IsOnline = false
	}
	return c.JSON(http.StatusOK, device)
}

func (H *HttpRoutes) GetAPIKey(c echo.Context) error {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, err.Error())
	}
	apikey, err := H.query.GetApiKey(c.Request().Context(), id)
	if err != nil {
		return c.JSON(500, err.Error())
	}
	return c.JSON(http.StatusOK, apikey)
}
