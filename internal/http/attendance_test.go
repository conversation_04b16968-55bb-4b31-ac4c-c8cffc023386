package http

import (
	"context"
	"database/sql"
	"fmt"
	"testing"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
)

// MockQueries implements a subset of database.Queries interface for testing attendance logic
type MockQueries struct {
	attendanceRecords map[string]bool // key: "userID-state-date", value: exists
}

func NewMockQueries() *MockQueries {
	return &MockQueries{
		attendanceRecords: make(map[string]bool),
	}
}

func (m *MockQueries) CheckAttendanceRecordExists(ctx context.Context, arg database.CheckAttendanceRecordExistsParams) (bool, error) {
	dateStr := arg.Time.Time.Format("2006-01-02")
	key := fmt.Sprintf("%d-%s-%s", arg.UserID, arg.State, dateStr)
	return m.attendanceRecords[key], nil
}

// Helper method to add attendance records for testing
func (m *MockQueries) AddAttendanceRecord(userID int64, state string, date time.Time) {
	dateStr := date.Format("2006-01-02")
	key := fmt.Sprintf("%d-%s-%s", userID, state, dateStr)
	m.attendanceRecords[key] = true
}

// TestCurrentAttendanceLogicError demonstrates the incorrect logic in qr-out validation
func TestCurrentAttendanceLogicError(t *testing.T) {
	mockQueries := NewMockQueries()
	ctx := context.Background()
	today := time.Now()
	userID := int64(123)

	// Test Case 1: Current INCORRECT logic - checking for qr-out instead of qr-in
	t.Run("Current_Logic_QR_Out_Validation", func(t *testing.T) {
		// Scenario: User has NO check-in record for today
		// Current logic checks: "Does user have qr-out record?" (WRONG)
		// Should check: "Does user have qr-in record?" (CORRECT)

		// Current logic from handlers.go lines 225-244:
		isAlreadyOut, err := mockQueries.CheckAttendanceRecordExists(ctx, database.CheckAttendanceRecordExistsParams{
			UserID: userID,
			State:  "qr-out", // ❌ WRONG: Checking for qr-out
			Time: sql.NullTime{
				Time:  today,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: "regular",
				Valid:  true,
			},
		})

		if err != nil {
			t.Fatalf("Database error: %v", err)
		}

		// Current logic: if isAlreadyOut is true, reject the request
		// Problem: User has no qr-out record, so isAlreadyOut = false
		// This means the request is ALLOWED, but it shouldn't be!
		// User should not be able to check out without checking in first.

		if !isAlreadyOut {
			t.Log("❌ CURRENT LOGIC ALLOWS checkout without checkin (INCORRECT)")
			t.Log("   User has no qr-out record, so checkout is allowed")
			t.Log("   But user also has no qr-in record, so this should be rejected!")
		} else {
			t.Log("✅ Current logic would reject (but for wrong reason)")
		}
	})

	// Test Case 2: What the CORRECT logic should be
	t.Run("Correct_Logic_QR_Out_Validation", func(t *testing.T) {
		// Correct logic should check: "Does user have qr-in record for today?"
		// If NO qr-in record exists, reject the qr-out request

		hasCheckedIn, err := mockQueries.CheckAttendanceRecordExists(ctx, database.CheckAttendanceRecordExistsParams{
			UserID: userID,
			State:  "qr-in", // ✅ CORRECT: Checking for qr-in
			Time: sql.NullTime{
				Time:  today,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: "regular",
				Valid:  true,
			},
		})

		if err != nil {
			t.Fatalf("Database error: %v", err)
		}

		if !hasCheckedIn {
			t.Log("✅ CORRECT LOGIC: Reject checkout - user hasn't checked in")
		} else {
			// Also need to check if user already checked out
			hasCheckedOut, err := mockQueries.CheckAttendanceRecordExists(ctx, database.CheckAttendanceRecordExistsParams{
				UserID: userID,
				State:  "qr-out",
				Time: sql.NullTime{
					Time:  today,
					Valid: true,
				},
				Booktype: sql.NullString{
					String: "regular",
					Valid:  true,
				},
			})

			if err != nil {
				t.Fatalf("Database error: %v", err)
			}

			if hasCheckedOut {
				t.Log("✅ CORRECT LOGIC: Reject checkout - user already checked out")
			} else {
				t.Log("✅ CORRECT LOGIC: Allow checkout - user checked in but not out")
			}
		}
	})

	// Test Case 3: Demonstrate the problem with different scenarios
	t.Run("Scenario_Analysis", func(t *testing.T) {
		scenarios := []struct {
			name          string
			hasCheckedIn  bool
			hasCheckedOut bool
			shouldAllow   bool
		}{
			{"No checkin, no checkout", false, false, false}, // Should reject
			{"Has checkin, no checkout", true, false, true},  // Should allow
			{"Has checkin, has checkout", true, true, false}, // Should reject
			{"No checkin, has checkout", false, true, false}, // Should reject (impossible but test anyway)
		}

		for _, scenario := range scenarios {
			t.Run(scenario.name, func(t *testing.T) {
				// Reset mock data
				mockQueries = NewMockQueries()

				if scenario.hasCheckedIn {
					mockQueries.AddAttendanceRecord(userID, "qr-in", today)
				}
				if scenario.hasCheckedOut {
					mockQueries.AddAttendanceRecord(userID, "qr-out", today)
				}

				// Test current INCORRECT logic
				currentLogicResult, _ := mockQueries.CheckAttendanceRecordExists(ctx, database.CheckAttendanceRecordExistsParams{
					UserID:   userID,
					State:    "qr-out", // Current logic checks for qr-out
					Time:     sql.NullTime{Time: today, Valid: true},
					Booktype: sql.NullString{String: "regular", Valid: true},
				})
				currentWouldAllow := !currentLogicResult

				// Test CORRECT logic
				hasCheckedIn, _ := mockQueries.CheckAttendanceRecordExists(ctx, database.CheckAttendanceRecordExistsParams{
					UserID:   userID,
					State:    "qr-in", // Correct logic checks for qr-in first
					Time:     sql.NullTime{Time: today, Valid: true},
					Booktype: sql.NullString{String: "regular", Valid: true},
				})

				hasCheckedOut, _ := mockQueries.CheckAttendanceRecordExists(ctx, database.CheckAttendanceRecordExistsParams{
					UserID:   userID,
					State:    "qr-out",
					Time:     sql.NullTime{Time: today, Valid: true},
					Booktype: sql.NullString{String: "regular", Valid: true},
				})

				correctWouldAllow := hasCheckedIn && !hasCheckedOut

				t.Logf("Scenario: %s", scenario.name)
				t.Logf("  Should allow: %v", scenario.shouldAllow)
				t.Logf("  Current logic allows: %v", currentWouldAllow)
				t.Logf("  Correct logic allows: %v", correctWouldAllow)

				if currentWouldAllow != scenario.shouldAllow {
					t.Logf("  ❌ Current logic is WRONG")
				} else {
					t.Logf("  ✅ Current logic happens to be correct")
				}

				if correctWouldAllow != scenario.shouldAllow {
					t.Logf("  ❌ Correct logic implementation error")
				} else {
					t.Logf("  ✅ Correct logic works as expected")
				}
			})
		}
	})
}

// TestFixedAttendanceLogic verifies that the fixed logic works correctly
func TestFixedAttendanceLogic(t *testing.T) {
	mockQueries := NewMockQueries()
	ctx := context.Background()
	today := time.Now()
	userID := int64(123)

	// Test the FIXED logic implementation
	t.Run("Fixed_Logic_Validation", func(t *testing.T) {
		scenarios := []struct {
			name          string
			hasCheckedIn  bool
			hasCheckedOut bool
			shouldAllow   bool
			expectedMsg   string
		}{
			{
				name:          "No checkin, no checkout - should reject",
				hasCheckedIn:  false,
				hasCheckedOut: false,
				shouldAllow:   false,
				expectedMsg:   "user must check in before checking out",
			},
			{
				name:          "Has checkin, no checkout - should allow",
				hasCheckedIn:  true,
				hasCheckedOut: false,
				shouldAllow:   true,
				expectedMsg:   "",
			},
			{
				name:          "Has checkin, has checkout - should reject",
				hasCheckedIn:  true,
				hasCheckedOut: true,
				shouldAllow:   false,
				expectedMsg:   "user already out",
			},
			{
				name:          "No checkin, has checkout - should reject",
				hasCheckedIn:  false,
				hasCheckedOut: true,
				shouldAllow:   false,
				expectedMsg:   "user must check in before checking out",
			},
		}

		for _, scenario := range scenarios {
			t.Run(scenario.name, func(t *testing.T) {
				// Reset mock data
				mockQueries = NewMockQueries()

				if scenario.hasCheckedIn {
					mockQueries.AddAttendanceRecord(userID, "qr-in", today)
				}
				if scenario.hasCheckedOut {
					mockQueries.AddAttendanceRecord(userID, "qr-out", today)
				}

				// Simulate the FIXED logic
				// Step 1: Check if user has checked in
				hasCheckedIn, err := mockQueries.CheckAttendanceRecordExists(ctx, database.CheckAttendanceRecordExistsParams{
					UserID:   userID,
					State:    "qr-in", // FIXED: Check for qr-in first
					Time:     sql.NullTime{Time: today, Valid: true},
					Booktype: sql.NullString{String: "regular", Valid: true},
				})
				if err != nil {
					t.Fatalf("Database error: %v", err)
				}

				var fixedLogicResult bool
				var resultMessage string

				if !hasCheckedIn {
					// User hasn't checked in, reject
					fixedLogicResult = false
					resultMessage = "user must check in before checking out"
				} else {
					// Step 2: Check if user has already checked out
					hasCheckedOut, err := mockQueries.CheckAttendanceRecordExists(ctx, database.CheckAttendanceRecordExistsParams{
						UserID:   userID,
						State:    "qr-out",
						Time:     sql.NullTime{Time: today, Valid: true},
						Booktype: sql.NullString{String: "regular", Valid: true},
					})
					if err != nil {
						t.Fatalf("Database error: %v", err)
					}

					if hasCheckedOut {
						// User already checked out, reject
						fixedLogicResult = false
						resultMessage = "user already out"
					} else {
						// User checked in but not out, allow
						fixedLogicResult = true
						resultMessage = ""
					}
				}

				// Verify the results
				t.Logf("Scenario: %s", scenario.name)
				t.Logf("  Expected allow: %v", scenario.shouldAllow)
				t.Logf("  Fixed logic allows: %v", fixedLogicResult)
				t.Logf("  Expected message: %s", scenario.expectedMsg)
				t.Logf("  Actual message: %s", resultMessage)

				if fixedLogicResult != scenario.shouldAllow {
					t.Errorf("❌ Fixed logic failed: expected %v, got %v", scenario.shouldAllow, fixedLogicResult)
				} else {
					t.Logf("✅ Fixed logic works correctly")
				}

				if scenario.expectedMsg != "" && resultMessage != scenario.expectedMsg {
					t.Errorf("❌ Wrong error message: expected '%s', got '%s'", scenario.expectedMsg, resultMessage)
				}
			})
		}
	})

	// Test comparison between old and new logic
	t.Run("Before_vs_After_Fix_Comparison", func(t *testing.T) {
		// Test the critical case: User with no check-in trying to check out
		mockQueries = NewMockQueries()
		// User has NO records at all

		// OLD (incorrect) logic
		oldLogicCheck, _ := mockQueries.CheckAttendanceRecordExists(ctx, database.CheckAttendanceRecordExistsParams{
			UserID:   userID,
			State:    "qr-out", // Old logic checked for qr-out
			Time:     sql.NullTime{Time: today, Valid: true},
			Booktype: sql.NullString{String: "regular", Valid: true},
		})
		oldLogicWouldAllow := !oldLogicCheck // Old logic: if no qr-out record, allow

		// NEW (fixed) logic
		newLogicCheckedIn, _ := mockQueries.CheckAttendanceRecordExists(ctx, database.CheckAttendanceRecordExistsParams{
			UserID:   userID,
			State:    "qr-in", // New logic checks for qr-in first
			Time:     sql.NullTime{Time: today, Valid: true},
			Booktype: sql.NullString{String: "regular", Valid: true},
		})
		newLogicWouldAllow := newLogicCheckedIn // New logic: only allow if checked in

		t.Logf("Critical test case: User with no check-in trying to check out")
		t.Logf("  Old logic would allow: %v (INCORRECT)", oldLogicWouldAllow)
		t.Logf("  New logic would allow: %v (CORRECT)", newLogicWouldAllow)

		if oldLogicWouldAllow {
			t.Log("✅ Confirmed: Old logic incorrectly allows checkout without checkin")
		}

		if !newLogicWouldAllow {
			t.Log("✅ Confirmed: New logic correctly rejects checkout without checkin")
		} else {
			t.Error("❌ New logic should reject checkout without checkin")
		}
	})
}

// TestEmergenceyExitFuncErrorHandling tests error scenarios in EmergenceyExitFunc
func TestEmergenceyExitFuncErrorHandling(t *testing.T) {
	t.Run("EmergenceyExitFunc_ErrorHandling_Issues", func(t *testing.T) {
		// This test demonstrates the issues with EmergenceyExitFunc

		t.Log("❌ ISSUE 1: EmergenceyExitFunc doesn't handle errors from SendMsg operations")
		t.Log("   - qrcli.SendMsg() can fail but error is ignored")
		t.Log("   - H.hub.SendToClient() can fail but error is ignored")

		t.Log("❌ ISSUE 2: No rollback mechanism for partial failures")
		t.Log("   - If some QR clients receive message but others fail, no cleanup")
		t.Log("   - Client state is reset but QR clients may be in inconsistent state")

		t.Log("❌ ISSUE 3: No validation of client state before operations")
		t.Log("   - Doesn't check if client is already in IDLE state")
		t.Log("   - Doesn't verify QR clients exist before sending messages")

		t.Log("❌ ISSUE 4: Potential nil pointer dereferences")
		t.Log("   - qrcli, _ := H.hub.GetClient(cli.Id) - ignores error")
		t.Log("   - qrcli.SendMsg() called without checking if qrcli is nil")

		t.Log("❌ ISSUE 5: No proper logging of failures")
		t.Log("   - Only logs 'client processing' regardless of actual error")
		t.Log("   - No structured logging with error details")

		// Demonstrate the current implementation issues
		currentImplementationIssues := []string{
			"No error handling for SendMsg operations",
			"No rollback mechanism for partial failures",
			"No validation of client state",
			"Potential nil pointer dereferences",
			"Poor error logging",
			"No atomic state management",
		}

		for i, issue := range currentImplementationIssues {
			t.Logf("Issue %d: %s", i+1, issue)
		}
	})

	t.Run("EmergenceyExitFunc_DesiredBehavior", func(t *testing.T) {
		t.Log("✅ DESIRED: Comprehensive error handling")
		t.Log("   - Check all return values from SendMsg operations")
		t.Log("   - Log specific errors with context")
		t.Log("   - Return aggregated error information")

		t.Log("✅ DESIRED: Atomic state management")
		t.Log("   - Validate current state before making changes")
		t.Log("   - Implement rollback mechanism for partial failures")
		t.Log("   - Ensure consistent state across all components")

		t.Log("✅ DESIRED: Robust client validation")
		t.Log("   - Verify QR clients exist before operations")
		t.Log("   - Check client connection health")
		t.Log("   - Handle nil client scenarios gracefully")

		t.Log("✅ DESIRED: Structured error reporting")
		t.Log("   - Return detailed error information")
		t.Log("   - Include context about which operations failed")
		t.Log("   - Provide actionable error messages")
	})
}
