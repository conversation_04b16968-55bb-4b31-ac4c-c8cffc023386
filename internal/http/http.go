package http

import (
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/config"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/ws"
	"github.com/labstack/echo/v4"
)

type HttpRoutes struct {
	e      *echo.Echo
	hub    *ws.Hub
	query  *database.Queries
	tstore *utils.TokenStore
	cfg    *config.Config
}

func NewHttpRoute(e *echo.Echo, hub *ws.Hub, query *database.Queries, tstore *utils.TokenStore, cfg *config.Config) *HttpRoutes {
	return &HttpRoutes{
		e:      e,
		hub:    hub,
		query:  query,
		tstore: tstore,
		cfg:    cfg,
	}
}

func (H *HttpRoutes) RegisterRouter() {
	httpRoute := H.e.Group("/http1")

	// door lock user routes
	// httpRoute.POST("/insertuser", H.InsertUser)
	httpRoute.GET("/getuser/:branch_id", H.GetUser)
	httpRoute.GET("/checkuser/:id", H.CheckUserExistace)

	// door lock routes
	httpRoute.POST("/createdevice", H.CreateDevice)
	httpRoute.GET("/getdevice", H.GetDevice)
	httpRoute.PUT("/updatedevice/:id", H.UpdateDevice)
	httpRoute.DELETE("/deletedevice/:id", H.DeleteDevice)
	httpRoute.GET("/getdevicecount", H.GetDeviceCount)
	httpRoute.GET("/getbranchcount", H.GetBranchesCount)
	httpRoute.GET("/getbranchdevices/:id", H.GetDevicesByBranch)
	httpRoute.GET("/getdevicebyid/:id", H.GetOneDevice)
	httpRoute.PUT("/updatedoorstatus/:id", H.UpdateDeviceStatus)
	httpRoute.GET("/getapikey/:id", H.GetAPIKey)

	// attendee routes
	httpRoute.POST("/createattendee", H.CreateAttendee)
	httpRoute.GET("/getallattendee", H.GetAllAttendee)
	httpRoute.GET("/getattendee/:id", H.GetAttendeeByID)
	httpRoute.GET("/getattendee", H.GetAttendeeByIDState)

	httpRoute.POST("/getattendeecount", H.GetAttendeeCounts)
	httpRoute.POST("/getattendeecountbybranch", H.GetAttendeeRecAll)

	httpRoute.GET("/all-act-devicecount", H.ActiveDeviceCountAll)
	httpRoute.POST("/getAttendeeRec", H.GetAttendeeRecAll)
	httpRoute.GET("/shopStore/:key", H.GetTokenStore)
	httpRoute.POST("/99emgexit99", H.EmergenceyOpenDoor)
}
