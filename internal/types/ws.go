package types

import "time"

type ConnMeta struct {
	// Send pings to peer with this period. Must be less than pongWait.
	PingTime time.Duration

	// Time allowed to read the next pong message from the peer
	PongTime time.Duration

	// Time allowed to write a message to the peer.
	WriteWait time.Duration
}

type SendWSMsg struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type ProcessingWSMsg struct {
	IsProcessing string `json:"isProcessing"`
}

type UnlockWSMsg struct {
	IsUnlock bool `json:"isUnlock"`
}

type ProcessingEventMsg struct {
	IsProcessing string `json:"isProcessing"`
	ClientId     string `json:"clientId"`
	ReqDeviceId  string `json:"reqDeviceId"`
}

type SSforClean struct {
	Code     int `json:"code"`
	BranchId int `json:"branchId"`
}
