package types

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
)

// this check
type DoorLockAccessReq struct {
	User      TmpUser `json:"user"`
	QRcode    string  `json:"qrcode"`
	Timestamp string  `json:"timestamp"`
}

type EmergencyDoorOpen struct {
	DeviceID string `json:"device_id"`
	BranchId int    `json:"branch_id"`
	Password string `json:"password"`
}

// TODO: Impliment Authentication
type QRCodeHTTPReq struct {
	BranchId int    `json:"branchId"`
	ClientId string `json:"clientId"`
}

type DisconnectDoorClinetType struct {
	BranchId int    `json:"branchId"`
	ClientId string `json:"clientId"`
}

type ServiceData struct {
	Sid         string `json:"sid"`
	Name        string `json:"name"`
	Description string `json:"description"`
	StartTime   string `json:"start_time"`
	EndTime     string `json:"end_time"`
}

type GymData struct {
	ID    int    `json:"id"`
	Name  string `json:"name"`
	GType string `json:"gtype"`
}

type TmpUser struct {
	ID           int          `json:"id"`
	Name         string       `json:"name"`
	Email        string       `json:"email"`
	Role         string       `json:"role"`
	Phone        string       `json:"phone"`
	NIC          string       `json:"nic"`
	AvatarURL    string       `json:"avatarUrl"`
	BookType     string       `json:"booktype"`
	ServiceData  *ServiceData `json:"service_data,omitempty"`
	GymData      GymData      `json:"gym_data"`
	Subscription interface{}  `json:"subscription"` // change to a struct if you know its shape
}

// For GymData struct
func GymDataToNullString(data *GymData) (sql.NullString, error) {
	// Check if data is nil
	if data == nil {
		return sql.NullString{Valid: false}, nil
	}

	// Marshal to JSON
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return sql.NullString{}, fmt.Errorf("failed to marshal gym data: %w", err)
	}

	return sql.NullString{
		String: string(jsonBytes),
		Valid:  true,
	}, nil
}

func NullStringToGymData(nullStr sql.NullString) (*GymData, error) {
	// Check if the string is valid
	if !nullStr.Valid {
		return nil, nil // Return nil if the string is null
	}

	// Unmarshal the JSON
	var gymData GymData
	err := json.Unmarshal([]byte(nullStr.String), &gymData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal gym data: %w", err)
	}

	return &gymData, nil
}

// For ServiceData struct
func ServiceDataToNullString(data *ServiceData) (sql.NullString, error) {
	// Check if data is nil
	if data == nil {
		return sql.NullString{Valid: false}, nil
	}

	// Marshal to JSON
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return sql.NullString{}, fmt.Errorf("failed to marshal service data: %w", err)
	}

	return sql.NullString{
		String: string(jsonBytes),
		Valid:  true,
	}, nil
}

func NullStringToServiceData(nullStr sql.NullString) (*ServiceData, error) {
	// Check if the string is valid
	if !nullStr.Valid {
		return nil, nil // Return nil if the string is null
	}

	// Unmarshal the JSON
	var serviceData ServiceData
	err := json.Unmarshal([]byte(nullStr.String), &serviceData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal service data: %w", err)
	}

	return &serviceData, nil
}

type AttendeeResponse struct {
	ID             int64        `json:"id"`
	AttendeeID     int64        `json:"attendee_id"`
	Time           *time.Time   `json:"time,omitempty"`
	State          string       `json:"state"`
	BranchID       int64        `json:"branch_id"`
	BranchName     string       `json:"branch_name"`
	DeviceID       *int64       `json:"device_id,omitempty"`
	UserID         int64        `json:"user_id"`
	SubscriptionID *int64       `json:"subscription_id"`
	LastIn         *time.Time   `json:"last_in,omitempty"`
	LastOut        *time.Time   `json:"last_out,omitempty"`
	CurrentState   string       `json:"current_state,omitempty"`
	Username       string       `json:"username"`
	Email          string       `json:"email,omitempty"`
	Role           string       `json:"role,omitempty"`
	Phone          string       `json:"phone,omitempty"`
	Nic            string       `json:"nic,omitempty"`
	AvatarUrl      string       `json:"avatar_url,omitempty"`
	UserCreatedAt  *time.Time   `json:"user_created_at,omitempty"`
	UserUpdatedAt  *time.Time   `json:"user_updated_at,omitempty"`
	Booktype       string       `json:"booktype,omitempty"`
	GymData        *GymData     `json:"gym_data,omitempty"`     // Parsed from text
	ServiceData    *ServiceData `json:"service_data,omitempty"` // Parsed from text
}

// Helper function to parse GymData from SQL NullString
func parseGymData(data sql.NullString) (*GymData, error) {
	if !data.Valid {
		return nil, nil
	}

	var gymData GymData
	err := json.Unmarshal([]byte(data.String), &gymData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal gym data: %w", err)
	}

	return &gymData, nil
}

// Helper function to parse ServiceData from SQL NullString
func parseServiceData(data sql.NullString) (*ServiceData, error) {
	if !data.Valid {
		return nil, nil
	}

	var serviceData ServiceData
	err := json.Unmarshal([]byte(data.String), &serviceData)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal service data: %w", err)
	}

	return &serviceData, nil
}

// Convert database row to response struct with parsed JSON fields
func ConvertToResponse(row *database.GetAttendeesByBranchWithPaginationRow) (*AttendeeResponse, error) {
	response := &AttendeeResponse{
		ID:         row.ID,
		AttendeeID: row.AttendeeID,
		State:      row.State,
		BranchID:   row.BranchID,
		BranchName: row.BranchName,
		UserID:     row.UserID,
		Username:   row.Username,
	}

	if row.SubscriptionID.Valid {
		response.SubscriptionID = &row.SubscriptionID.Int64
	}

	// Handle nullable fields
	if row.Time.Valid {
		response.Time = &row.Time.Time
	}

	if row.DeviceID.Valid {
		deviceID := row.DeviceID.Int64
		response.DeviceID = &deviceID
	}

	if row.LastIn.Valid {
		response.LastIn = &row.LastIn.Time
	}

	if row.LastOut.Valid {
		response.LastOut = &row.LastOut.Time
	}

	if row.CurrentState.Valid {
		response.CurrentState = row.CurrentState.String
	}

	if row.Email.Valid {
		response.Email = row.Email.String
	}

	if row.Role.Valid {
		response.Role = row.Role.String
	}

	if row.Phone.Valid {
		response.Phone = row.Phone.String
	}

	if row.Nic.Valid {
		response.Nic = row.Nic.String
	}

	if row.AvatarUrl.Valid {
		response.AvatarUrl = row.AvatarUrl.String
	}

	if row.UserCreatedAt.Valid {
		response.UserCreatedAt = &row.UserCreatedAt.Time
	}

	if row.UserUpdatedAt.Valid {
		response.UserUpdatedAt = &row.UserUpdatedAt.Time
	}

	if row.Booktype.Valid {
		response.Booktype = row.Booktype.String
	}

	// Parse the JSON in GymData
	if row.GymData.Valid {
		gymData, err := parseGymData(row.GymData)
		if err != nil {
			return nil, err
		}
		response.GymData = gymData
	}

	// Parse the JSON in ServiceData
	if row.ServiceData.Valid {
		serviceData, err := parseServiceData(row.ServiceData)
		if err != nil {
			return nil, err
		}
		response.ServiceData = serviceData
	}

	return response, nil
}
