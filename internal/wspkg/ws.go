package wspkg

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/labstack/echo/v4"
)

type WebSocketCtx struct {
	manger      *Manager
	db          *database.Queries
	tstore      *utils.TokenStore
	ClientState *ClientState
}

func NewWebSocketCtx(manger *Manager, db *database.Queries, tstore *utils.TokenStore) *WebSocketCtx {
	return &WebSocketCtx{
		manger:      manger,
		db:          db,
		tstore:      tstore,
		ClientState: NewClientState(),
	}
}

func (W *WebSocketCtx) AuthenticateClient(c echo.Context) error {
	// ... existing authentication code ...
	var conntypereq types.WSConnectReq
	err := c.Bind(&conntypereq)
	if err != nil {
		W.manger.logger.Error(
			"Bind Error @ AuthenticateClient",
			Field{Key: "Error", Value: err},
			Field{Key: "Request", Value: c.Request().Body},
		)
		return c.String(http.StatusBadRequest, "the request json invalid")
	}
	checkData := database.ValidateDeviceDetailsParams{
		ID:       int64(conntypereq.Id),
		ApiKey:   utils.HashAPIKey(conntypereq.APIKey),
		BranchID: int64(conntypereq.BranchId),
	}
	W.manger.logger.Debug(
		"Checking Device Details @ AuthenticateClient",
		Field{Key: "CheckData", Value: checkData},
		Field{Key: "Request", Value: conntypereq},
	)
	isValid, err := W.db.ValidateDeviceDetails(c.Request().Context(), checkData)
	if err != nil {
		W.manger.logger.Error(
			"Database Error @ AuthenticateClient",
			Field{Key: "Error", Value: err},
			Field{Key: "Request", Value: conntypereq},
		)
		return c.JSON(http.StatusUnauthorized, "Api key error")
	}
	if len(isValid) <= 0 {
		W.manger.logger.Error(
			"Invalid API Key @ AuthenticateClient",
			Field{
				Key:   "Request",
				Value: conntypereq,
			},
		)
		return c.JSON(http.StatusUnauthorized, "Api key not valid with credintial")
	}

	// checkApi, err := utils.ValidateAPIKey(W.query, int64(conntypereq.Id), conntypereq.APIKey)
	// if err != nil {
	// 	return c.JSON(http.StatusUnauthorized, "Api key error")
	// }
	// if !checkApi {
	// 	return c.JSON(http.StatusUnauthorized, "Api key not valid")
	// }

	// Generate token
	token := utils.GenerateSecureToken()

	// Store token with 2-minute expiration
	W.tstore.StoreToken(
		token,
		strconv.Itoa(conntypereq.Id),
		conntypereq.ConnType,
		conntypereq.BranchId,
		"",
		conntypereq.APIKey,
		time.Now().Add(5*time.Minute),
	)

	return c.JSON(http.StatusOK, map[string]string{"token": token})
}

func (W *WebSocketCtx) DoorClientWS(c echo.Context) error {
	token := c.QueryParam("token")
	tokenData, isValid := W.tstore.ValidateToken(token)
	if !isValid {
		W.manger.logger.Error(
			"Token Validation Error @ DoorClientWS",
			Field{Key: "Token", Value: token},
			Field{Key: "Validate", Value: isValid},
		)
		return c.JSON(http.StatusUnauthorized, "Invalid token")
	}

	W.tstore.RemoveToken(token)

	if tokenData.ConnType != "door-lock" {
		W.manger.logger.Error(
			"Connection Type Error @ DoorClientWS",
			Field{Key: "TokenData", Value: tokenData},
		)
		return c.JSON(http.StatusBadRequest, "Invalid connection type")
	}

	deviceId, err := strconv.ParseInt(tokenData.ClientId, 10, 64)
	if err != nil {
		W.manger.logger.Error(
			"invalid Client ID @ DoorClientWS",
			Field{Key: "ClientId", Value: tokenData.ClientId},
		)
		return c.JSON(http.StatusBadRequest, "Invalid client ID")
	}

	deviceData, err := W.db.GetDeviceData(c.Request().Context(), deviceId)
	if err != nil {
		W.manger.logger.Error(
			"Error fetching device data @ DoorClientWS",
			Field{Key: "DeviceId", Value: deviceId},
		)
	}

	conn, err := W.manger.upgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		W.manger.logger.Error(
			"websocket upgrade error @ DoorClientWS",
			Field{Key: "Error", Value: err},
		)

		return c.JSON(http.StatusInternalServerError, "Failed to upgrade connection")
	}

	connClient := NewClient(conn)
	connClient.SetOnline(true)
	connClient.SetCloseHandler(func(c *Client) error {
		W.ClientState.RemoveDoorClient(connClient.Id)
		return nil
	})
	connClient.SetErrHandler(func(err error) {
		W.manger.logger.Error(
			fmt.Sprintf("Error in client %s", connClient.Id),
			Field{Key: "Error", Value: err},
		)
	})

	W.ClientState.CreateDoorClient(connClient.Id, deviceData.ID, deviceData.DeviceName, int(deviceData.BranchID))
}
