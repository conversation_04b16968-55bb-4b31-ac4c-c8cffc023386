package wspkg

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

type WebSocketCtx struct {
	manger      *Manager
	db          *database.Queries
	tstore      *utils.TokenStore
	ClientState *ClientState
}

func NewWebSocketCtx(manger *Manager, db *database.Queries, tstore *utils.TokenStore) *WebSocketCtx {
	return &WebSocketCtx{
		manger:      manger,
		db:          db,
		tstore:      tstore,
		ClientState: NewClientState(),
	}
}

func (W *WebSocketCtx) AuthenticateClient(c echo.Context) error {
	// ... existing authentication code ...
	var conntypereq types.WSConnectReq
	err := c.Bind(&conntypereq)
	if err != nil {
		W.manger.logger.Error(
			"Bind Error @ AuthenticateClient",
			Field{Key: "Error", Value: err},
			Field{Key: "Request", Value: c.Request().Body},
		)
		return c.String(http.StatusBadRequest, "the request json invalid")
	}
	checkData := database.ValidateDeviceDetailsParams{
		ID:       int64(conntypereq.Id),
		ApiKey:   utils.HashAPIKey(conntypereq.APIKey),
		BranchID: int64(conntypereq.BranchId),
	}
	W.manger.logger.Debug(
		"Checking Device Details @ AuthenticateClient",
		Field{Key: "CheckData", Value: checkData},
		Field{Key: "Request", Value: conntypereq},
	)
	isValid, err := W.db.ValidateDeviceDetails(c.Request().Context(), checkData)
	if err != nil {
		W.manger.logger.Error(
			"Database Error @ AuthenticateClient",
			Field{Key: "Error", Value: err},
			Field{Key: "Request", Value: conntypereq},
		)
		return c.JSON(http.StatusUnauthorized, "Api key error")
	}
	if len(isValid) <= 0 {
		W.manger.logger.Error(
			"Invalid API Key @ AuthenticateClient",
			Field{
				Key:   "Request",
				Value: conntypereq,
			},
		)
		return c.JSON(http.StatusUnauthorized, "Api key not valid with credintial")
	}

	// checkApi, err := utils.ValidateAPIKey(W.query, int64(conntypereq.Id), conntypereq.APIKey)
	// if err != nil {
	// 	return c.JSON(http.StatusUnauthorized, "Api key error")
	// }
	// if !checkApi {
	// 	return c.JSON(http.StatusUnauthorized, "Api key not valid")
	// }

	// Generate token
	token := utils.GenerateSecureToken()

	// Store token with 2-minute expiration
	W.tstore.StoreToken(
		token,
		strconv.Itoa(conntypereq.Id),
		conntypereq.ConnType,
		conntypereq.BranchId,
		"",
		conntypereq.APIKey,
		time.Now().Add(5*time.Minute),
	)

	return c.JSON(http.StatusOK, map[string]string{"token": token})
}

func (W *WebSocketCtx) DoorClientWS(c echo.Context) error {
	token := c.QueryParam("token")
	tokenData, isValid := W.tstore.ValidateToken(token)
	if !isValid {
		W.manger.logger.Error(
			"Token Validation Error @ DoorClientWS",
			Field{Key: "Token", Value: token},
			Field{Key: "Validate", Value: isValid},
		)
		return c.JSON(http.StatusUnauthorized, "Invalid token")
	}

	W.tstore.RemoveToken(token)

	if tokenData.ConnType != "door-lock" {
		W.manger.logger.Error(
			"Connection Type Error @ DoorClientWS",
			Field{Key: "TokenData", Value: tokenData},
		)
		return c.JSON(http.StatusBadRequest, "Invalid connection type")
	}

	deviceId, err := strconv.ParseInt(tokenData.ClientId, 10, 64)
	if err != nil {
		W.manger.logger.Error(
			"invalid Client ID @ DoorClientWS",
			Field{Key: "ClientId", Value: tokenData.ClientId},
		)
		return c.JSON(http.StatusBadRequest, "Invalid client ID")
	}

	deviceData, err := W.db.GetDeviceData(c.Request().Context(), deviceId)
	if err != nil {
		W.manger.logger.Error(
			"Error fetching device data @ DoorClientWS",
			Field{Key: "DeviceId", Value: deviceId},
		)
		return c.JSON(http.StatusInternalServerError, "Failed to fetch device data")
	}

	conn, err := W.manger.upgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		W.manger.logger.Error(
			"websocket upgrade error @ DoorClientWS",
			Field{Key: "Error", Value: err},
		)

		return c.JSON(http.StatusInternalServerError, "Failed to upgrade connection")
	}

	connClient := NewClient(conn)
	connClient.SetOnline(true)
	connClient.SetCloseHandler(func(c *Client) error {
		W.ClientState.RemoveDoorClient(connClient.Id)
		return nil
	})
	connClient.SetErrHandler(func(err error) {
		W.manger.logger.Error(
			fmt.Sprintf("Error in client %s", connClient.Id),
			Field{Key: "Error", Value: err},
		)
	})

	W.ClientState.CreateDoorClient(connClient.Id, deviceData.ID, deviceData.DeviceName, int(deviceData.BranchID))

	isAdd := W.manger.AddExistingClient(connClient)
	if !isAdd {
		conn.Close()                                  // Clean up the connection
		W.ClientState.RemoveDoorClient(connClient.Id) // Clean up client state
		W.manger.logger.Error(
			"error adding existing client @ DoorClientWS",
		)
		return c.JSON(http.StatusInternalServerError, "Failed to add existing client")
	}

	go W.manger.ReadPump(connClient)
	go W.manger.WritePump(connClient)

	return nil
}

func (W *WebSocketCtx) AuthenticateQRClient(c echo.Context) error {
	var conntypereq types.QRConnectReq
	err := c.Bind(&conntypereq)
	if err != nil {
		W.manger.logger.Error(
			"websocket bind error @ AuthenticateQRClient",
			Field{Key: "Error", Value: err},
		)
		return c.String(http.StatusBadRequest, "the request json invalid")
	}

	reqDeviceId, err := strconv.ParseInt(conntypereq.ReqDeviceId, 10, 64)
	if err != nil {
		W.manger.logger.Error(
			"Error parsing ReqDeviceId @ AuthenticateQRClient",
			Field{Key: "ReqDeviceId", Value: conntypereq.ReqDeviceId},
		)
		return c.String(http.StatusBadRequest, "invalid device ID")
	}
	checkDevice := W.ClientState.GetDoorClientsByDoorId(reqDeviceId)
	if checkDevice == nil {
		W.manger.logger.Error(
			"Device Not Found @ AuthenticateQRClient",
			Field{Key: "ReqDeviceId", Value: conntypereq.ReqDeviceId},
		)
		return c.String(http.StatusBadRequest, "device not found")
	}

	if conntypereq.ConnType == "qr-in" {
		if checkDevice.qrIn != nil {
			return c.String(http.StatusBadRequest, "qr-In already exists for this door lock")
		}
	} else if conntypereq.ConnType == "qr-out" {
		if checkDevice.qrOut != nil {
			return c.String(http.StatusBadRequest, "qr-out already exists for this door lock")
		}
	} else {
		return c.String(http.StatusBadRequest, "invalid connection type")
	}

	// Generate token
	token := utils.GenerateSecureToken()

	// Generate UUID for QR client - this will be the actual client ID
	qrClientId := uuid.New().String()

	// Store token with 2-minute expiration
	W.tstore.StoreToken(
		token,
		qrClientId,
		conntypereq.ConnType,
		conntypereq.BranchId,
		conntypereq.ReqDeviceId,
		"",
		time.Now().Add(2*time.Minute),
	)

	// Return both token and the client ID that will be assigned
	return c.JSON(http.StatusOK, map[string]string{
		"token":    token,
		"clientId": qrClientId,
	})
}

func (W *WebSocketCtx) QRClientConnectingHandler(c echo.Context) error {
	token := c.QueryParam("token")
	tokenInfo, isValid := W.tstore.ValidateToken(token)
	if !isValid {
		return c.String(http.StatusUnauthorized, "token access issue")
	}

	W.tstore.RemoveToken(token)
	reqDeviceId, err := strconv.ParseInt(tokenInfo.ReqId, 10, 64)
	if err != nil {
		W.manger.logger.Error(
			"error parsing ReqId @ QRClientConnectingHandler",
			Field{Key: "ReqId", Value: tokenInfo.ReqId},
		)
		return c.String(http.StatusBadRequest, "invalid device ID")
	}

	checkDevice := W.ClientState.GetDoorClientsByDoorId(reqDeviceId)
	if checkDevice == nil {
		W.manger.logger.Error(
			"error getting door client @ QRClientConnectingHandler",
		)
		return c.String(http.StatusBadRequest, "device not found")
	}

	conn, err := W.manger.upgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		W.manger.logger.Error(
			"Error upgrading connection @ QRClientConnectingHandler",
			Field{Key: "Error", Value: err},
		)
	}

	client := W.manger.AddClient(conn, true)
	client.SetOnline(true)
	client.SetCloseHandler(func(c *Client) error {
		W.ClientState.RemoveQRClient(client.Id)
		return nil
	})
	client.SetErrHandler(func(err error) {
		W.manger.logger.Error(
			"Error in client connection @ QRClientConnectingHandler",
			Field{Key: "Error", Value: err},
		)
	})

	isAdd := W.manger.AddExistingClient(client)
	if !isAdd {
		conn.Close() // Clean up the connection
		return c.String(http.StatusInternalServerError, "Failed to add existing client")
	}

	switch tokenInfo.ConnType {
	case "qr-in":
		W.ClientState.CreateQRClient(client.Id, "qr-in", tokenInfo.BranchId, reqDeviceId)
	case "qr-out":
		W.ClientState.CreateQRClient(client.Id, "qr-out", tokenInfo.BranchId, reqDeviceId)
	default:
		return c.String(http.StatusBadRequest, "invalid connection type")
	}

	go W.manger.ReadPump(client)
	go W.manger.WritePump(client)

	return nil
}
