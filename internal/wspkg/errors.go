package wspkg

import (
	"fmt"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Field represents a key-value pair for structured logging
type Field struct {
	Key   string
	Value interface{}
}

// Logger interface for pluggable logging (framework-agnostic)
type Logger interface {
	Debug(msg string, fields ...Field)
	Info(msg string, fields ...Field)
	Warn(msg string, fields ...Field)
	Error(msg string, fields ...Field)
	Fatal(msg string, fields ...Field)
	With(fields ...Field) Logger
	Sync() error
}

// Framework-agnostic field constructors
func String(key, value string) Field {
	return Field{Key: key, Value: value}
}

func Int(key string, value int) Field {
	return Field{Key: key, Value: value}
}

func Bool(key string, value bool) Field {
	return Field{Key: key, Value: value}
}

func Error(err error) Field {
	return Field{Key: "error", Value: err}
}

func Any(key string, value interface{}) Field {
	return Field{Key: key, Value: value}
}

func Strings(key string, values []string) Field {
	return Field{Key: key, Value: values}
}

// ZapLogger wraps zap logger and implements the pluggable Logger interface
type ZapLogger struct {
	logger *zap.Logger
}

// NewDefaultLogger creates a new logger with development configuration
func NewDefaultLogger() (Logger, error) {
	logger, err := zap.NewDevelopment()
	if err != nil {
		return nil, fmt.Errorf("failed to create development logger: %w", err)
	}
	return &ZapLogger{logger: logger}, nil
}

// NewProductionLogger creates a new logger with production configuration
func NewProductionLogger() (Logger, error) {
	logger, err := zap.NewProduction()
	if err != nil {
		return nil, fmt.Errorf("failed to create production logger: %w", err)
	}
	return &ZapLogger{logger: logger}, nil
}

// NewZapLogger creates a logger with custom configuration
func NewZapLogger(level zapcore.Level, outputPaths []string) (Logger, error) {
	config := zap.Config{
		Level:       zap.NewAtomicLevelAt(level),
		Development: false,
		Sampling: &zap.SamplingConfig{
			Initial:    100,
			Thereafter: 100,
		},
		Encoding:         "json",
		EncoderConfig:    zap.NewProductionEncoderConfig(),
		OutputPaths:      outputPaths,
		ErrorOutputPaths: []string{"stderr"},
	}

	logger, err := config.Build()
	if err != nil {
		return nil, fmt.Errorf("failed to create custom logger: %w", err)
	}
	return &ZapLogger{logger: logger}, nil
}

func (l *ZapLogger) Debug(msg string, fields ...Field) {
	if l.logger != nil {
		l.logger.Debug(msg, l.convertFields(fields)...)
	}
}

func (l *ZapLogger) Info(msg string, fields ...Field) {
	if l.logger != nil {
		l.logger.Info(msg, l.convertFields(fields)...)
	}
}

func (l *ZapLogger) Warn(msg string, fields ...Field) {
	if l.logger != nil {
		l.logger.Warn(msg, l.convertFields(fields)...)
	}
}

func (l *ZapLogger) Error(msg string, fields ...Field) {
	if l.logger != nil {
		l.logger.Error(msg, l.convertFields(fields)...)
	}
}

func (l *ZapLogger) Fatal(msg string, fields ...Field) {
	if l.logger != nil {
		l.logger.Fatal(msg, l.convertFields(fields)...)
	}
}

func (l *ZapLogger) With(fields ...Field) Logger {
	if l.logger != nil {
		return &ZapLogger{logger: l.logger.With(l.convertFields(fields)...)}
	}
	return l
}

func (l *ZapLogger) Sync() error {
	if l.logger != nil {
		return l.logger.Sync()
	}
	return nil
}

// convertFields converts generic Fields to zap.Field
func (l *ZapLogger) convertFields(fields []Field) []zap.Field {
	zapFields := make([]zap.Field, len(fields))
	for i, field := range fields {
		switch v := field.Value.(type) {
		case string:
			zapFields[i] = zap.String(field.Key, v)
		case int:
			zapFields[i] = zap.Int(field.Key, v)
		case bool:
			zapFields[i] = zap.Bool(field.Key, v)
		case []string:
			zapFields[i] = zap.Strings(field.Key, v)
		case error:
			if field.Key == "error" {
				zapFields[i] = zap.Error(v)
			} else {
				zapFields[i] = zap.NamedError(field.Key, v)
			}
		default:
			zapFields[i] = zap.Any(field.Key, v)
		}
	}
	return zapFields
}

// NoOpLogger provides a logger that does nothing (useful for testing)
type NoOpLogger struct{}

func NewNoOpLogger() Logger {
	return &NoOpLogger{}
}

func (l *NoOpLogger) Debug(msg string, fields ...Field) {}
func (l *NoOpLogger) Info(msg string, fields ...Field)  {}
func (l *NoOpLogger) Warn(msg string, fields ...Field)  {}
func (l *NoOpLogger) Error(msg string, fields ...Field) {}
func (l *NoOpLogger) Fatal(msg string, fields ...Field) {}
func (l *NoOpLogger) With(fields ...Field) Logger       { return l }
func (l *NoOpLogger) Sync() error                       { return nil }

// ErrorHandler interface for pluggable error handling
type ErrorHandler interface {
	HandleError(err error, context map[string]interface{})
}

// DefaultErrorHandler provides basic error handling
type DefaultErrorHandler struct {
	logger Logger
}

func NewDefaultErrorHandler(logger Logger) ErrorHandler {
	return &DefaultErrorHandler{logger: logger}
}

func (h *DefaultErrorHandler) HandleError(err error, context map[string]interface{}) {
	fields := make([]Field, 0, len(context)+1)
	for k, v := range context {
		fields = append(fields, Any(k, v))
	}
	fields = append(fields, Error(err))
	h.logger.Error("Error occurred", fields...)
}
