package wspkg

import (
	"errors"
	"sync"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/google/uuid"
)

type DoorClient struct {
	DoorId       int64
	Name         string
	BranchId     int
	isOpen       bool
	qrIn         *QRClient
	qrOut        *QRClient
	onlineTime   time.Time
	lastCmd      time.Time
	secKey       string
	isprocessing bool
	isError      bool
	state        int
	mut          sync.RWMutex
}

func NewDoorClient(doorId int64, name string, branchId int) *DoorClient {
	secKey := uuid.New().String()

	return &DoorClient{
		DoorId:       doorId,
		Name:         name,
		BranchId:     branchId,
		isOpen:       false,
		qrIn:         nil,
		qrOut:        nil,
		isprocessing: false,
		onlineTime:   time.Now(),
		secKey:       secKey,
		state:        code.IDEL,
		mut:          sync.RWMutex{},
	}
}

func (d *DoorClient) SetQRIn(client *QRClient) error {
	d.mut.Lock()
	defer d.mut.Unlock()

	if client.CheckType() == "in" {
		d.qrIn = client
		return nil
	}

	return errors.New("clinet need to be [in] type")
}

func (d *DoorClient) SetQROut(client *QRClient) error {
	d.mut.Lock()
	defer d.mut.Unlock()

	if client.CheckType() == "out" {
		d.qrOut = client
		return nil
	}
	return errors.New("clinet need to be [out] type")
}

func (d *DoorClient) SetIsProcessing(b bool) {
	d.mut.Lock()
	defer d.mut.Unlock()

	d.isprocessing = b
}

func (d *DoorClient) CheckIsProcessing() bool {
	d.mut.RLock()
	defer d.mut.RUnlock()

	return d.isprocessing
}

func (d *DoorClient) SetIsOpen(b bool) {
	d.mut.Lock()
	defer d.mut.Unlock()

	d.isOpen = b
}

func (d *DoorClient) IsOpen() bool {
	d.mut.RLock()
	defer d.mut.RUnlock()
	return d.isOpen
}

func (d *DoorClient) SetState(state int) {
	d.mut.Lock()
	defer d.mut.Unlock()

	d.state = state
}

func (d *DoorClient) GetState() int {
	d.mut.RLock()
	defer d.mut.RUnlock()
	return d.state
}

func (d *DoorClient) GetSecKey() string {
	d.mut.RLock()
	defer d.mut.RUnlock()
	return d.secKey
}

func (d *DoorClient) GetOnlineTime() time.Time {
	d.mut.RLock()
	defer d.mut.RUnlock()
	return d.onlineTime
}

func (d *DoorClient) GetLastCmd() time.Time {
	d.mut.RLock()
	defer d.mut.RUnlock()
	return d.lastCmd
}

func (d *DoorClient) SetLastCmd(t time.Time) {
	d.mut.Lock()
	defer d.mut.Unlock()
	d.lastCmd = t
}

func (d *DoorClient) GetQRIn() *QRClient {
	d.mut.RLock()
	defer d.mut.RUnlock()
	if !d.qrIn.CheckValid() {
		return nil
	}
	return d.qrIn
}

func (d *DoorClient) GetQROut() *QRClient {
	d.mut.RLock()
	defer d.mut.RUnlock()
	if !d.qrOut.CheckValid() {
		return nil
	}
	return d.qrOut
}

func (d *DoorClient) GenNewSecKey() {
	secKey := uuid.New().String()
	d.mut.Lock()
	d.secKey = secKey
	d.mut.Unlock()
}

type QRClient struct {
	Id           string
	Typ          string
	BranchId     int
	doorId       int
	isprocessing bool
	isError      bool
	onlineTime   time.Time
	state        int
	valid        bool
	mut          sync.RWMutex
}

func NewQRClient(typ string, branchId int, Id string, door int) *QRClient {
	return &QRClient{
		Id:           Id,
		Typ:          typ,
		BranchId:     branchId,
		doorId:       door,
		isprocessing: false,
		isError:      false,
		onlineTime:   time.Now(),
		state:        code.IDEL,
		valid:        true,
		mut:          sync.RWMutex{},
	}
}

func (q *QRClient) CheckType() string {
	q.mut.RLock()
	defer q.mut.RUnlock()
	return q.Typ
}

func (q *QRClient) CheckValid() bool {
	q.mut.RLock()
	defer q.mut.RUnlock()
	return q.valid
}

func (q *QRClient) SetValid(b bool) {
	q.mut.Lock()
	defer q.mut.Unlock()
	q.valid = b
}

func (q *QRClient) SetIsProcessing(b bool) {
	q.mut.Lock()
	q.isprocessing = b
	q.mut.Unlock()
}

func (q *QRClient) CheckIsProcessing() bool {
	q.mut.RLock()
	defer q.mut.RUnlock()

	return q.isprocessing
}

func (q *QRClient) SetIsError(b bool) {
	q.mut.Lock()
	defer q.mut.Unlock()

	q.isError = b
}

func (q *QRClient) CheckIsError() bool {
	q.mut.RLock()
	defer q.mut.RUnlock()
	return q.isError
}

func (q *QRClient) SetState(state int) {
	q.mut.Lock()
	defer q.mut.Unlock()
	q.state = state
}

func (q *QRClient) GetState() int {
	q.mut.RLock()
	defer q.mut.RUnlock()
	return q.state
}

func (q *QRClient) GetOnlineTime() time.Time {
	q.mut.RLock()
	defer q.mut.RUnlock()
	return q.onlineTime
}

type ClientState struct {
	DoorLocks map[string]*DoorClient
	QRDevice  map[string]*QRClient
	mut       sync.RWMutex
}

func NewClientState() *ClientState {
	return &ClientState{
		DoorLocks: make(map[string]*DoorClient, 5),
		QRDevice:  make(map[string]*QRClient, 10),
		mut:       sync.RWMutex{},
	}
}

func (c *ClientState) CreateDoorClient(clientId string, doorId int64, name string, branchId int) {
	c.mut.Lock()
	defer c.mut.Unlock()

	doorClient := NewDoorClient(doorId, name, branchId)
	c.DoorLocks[clientId] = doorClient
}

func (c *ClientState) GetDoorClient(clientId string) (*DoorClient, error) {
	c.mut.RLock()
	defer c.mut.RUnlock()

	res, ok := c.DoorLocks[clientId]
	if !ok {
		return nil, errors.New("door client not found")
	}

	return res, nil
}

func (c *ClientState) CreateQRClient(clientId string, typ string, branchId int, door int) {
	c.mut.Lock()
	defer c.mut.Unlock()

	qrClient := NewQRClient(typ, branchId, clientId, door)
	c.QRDevice[clientId] = qrClient
}

func (c *ClientState) GetQRClient(clientId string) (*QRClient, error) {
	c.mut.RLock()
	defer c.mut.RUnlock()

	res, ok := c.QRDevice[clientId]
	if !ok {
		return nil, errors.New("qr client not found")
	}

	if res.CheckValid() == false {
		return nil, errors.New("qr client is not valid")
	}

	return res, nil
}

func (c *ClientState) RemoveDoorClient(clientId string) {
	c.mut.Lock()
	defer c.mut.Unlock()
	if _, ok := c.DoorLocks[clientId]; !ok {
		return
	}

	delete(c.DoorLocks, clientId)
}

func (c *ClientState) RemoveQRClient(clientId string) {
	c.mut.Lock()
	defer c.mut.Unlock()

	if _, ok := c.QRDevice[clientId]; !ok {
		return
	}
	c.QRDevice[clientId].SetValid(false)
	delete(c.QRDevice, clientId)
}

func (c *ClientState) GetDoorClientsByBranch(id int) []*DoorClient {
	c.mut.RLock()
	defer c.mut.RUnlock()

	var res []*DoorClient
	for _, door := range c.DoorLocks {
		if door.BranchId == id {
			res = append(res, door)
		}
	}

	return res
}

func (c *ClientState) GetQRClientsByBranch(id int) []*QRClient {
	c.mut.RLock()
	defer c.mut.RUnlock()
	var res []*QRClient

	for _, qr := range c.QRDevice {
		if qr.BranchId == id && qr.CheckValid() {
			res = append(res, qr)
		}
	}

	return res
}

func (c *ClientState) GetDoorClientsByDoorId(doorId int64) *DoorClient {
	c.mut.RLock()
	defer c.mut.RUnlock()

	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			return door
		}
	}
	return nil
}
