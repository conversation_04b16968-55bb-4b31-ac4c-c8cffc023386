package wspkg

import "errors"

type EventRouter struct {
	handlers map[string]func(c Ctx) error
}

func NewEventRouter() *EventRouter {
	return &EventRouter{
		handlers: make(map[string]func(c Ctx) error),
	}
}

func (e *EventRouter) On(event string, handler func(c Ctx) error) {
	if e.handlers == nil {
		e.handlers = make(map[string]func(c Ctx) error)
	}
	e.handlers[event] = handler
}

func (e *EventRouter) Handle(c Ctx) error {
	if handler, ok := e.handlers[c.Data.Event]; ok {
		err := handler(c)
		if err != nil {
			return err
		}
		return nil
	} else {
		return errors.New("event not found")
	}
}
