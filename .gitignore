# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
main

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

# Environment files
.env
*.env

# Database files
*.sql.dump

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log

# Binary files
bin/
dist/

# Local development configurations
config.local.yaml
config.dev.yaml

# Docker volumes
docker/data/
mariadb_data/

# Temporary files
tmp/
temp/

# Generated files
*.generated.go


/frontend/node_modules/
